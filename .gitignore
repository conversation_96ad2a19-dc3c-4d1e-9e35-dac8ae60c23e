# Vendor dependencies (Composer)
/vendor/
vendor.zip

# Environment and configuration files
.env
.env.local
.env.production
config.php
database.php

# Logs
error_log
*.log
logs/

# Cache and temporary files
/cache/
/tmp/
/temp/
/uploads/temp/

# User uploads (you may want to version control some of these)
/uploads/*.png
/uploads/*.jpg
/uploads/*.jpeg
/uploads/*.gif
/uploads/*.pdf
/uploads/*.doc
/uploads/*.docx
/uploads/*.xls
/uploads/*.xlsx
/uploads/*

# Generated PDFs and barcodes
/print/*.pdf

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Backup files
*.backup
*.bak
*.old

# Development and testing
/tests/
/test/
phpunit.xml
.phpunit.result.cache

# Node.js (if using)
node_modules/
npm-debug.log
yarn-error.log

# Compiled assets
*.min.js
*.min.css

# Database backup files
*.sql.gz
*.sql.bak
database/backups/

# Personal notes and documentation that shouldn't be shared
NOTES.md
TODO.md
PRIVATE.md