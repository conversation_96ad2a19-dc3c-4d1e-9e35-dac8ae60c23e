# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Environment

This is a PHP-based inventory management system running on XAMPP:
- **PHP Version**: Requires PHP 7.4+ (using features like typed properties, null coalescing)
- **Database**: MySQL (via PDO and Illuminate/Database ORM)
- **Web Server**: Apache (XAMPP)
- **Platform**: Windows (C:\xampp\htdocs\stockv3)

## Essential Commands

### Installation & Setup
```bash
# Install PHP dependencies
composer install

# Database setup - run SQL files in order:
# 1. Create database tables (check database/ directory for migrations)
# 2. Run any .sql files in the root directory
```

### Development
```bash
# No build process required - PHP files are interpreted directly
# Access the application at: http://localhost/stockv3/

# Clear application cache (if exists)
rm -rf cache/*

# Run composer autoload regeneration after adding new classes
composer dump-autoload
```

## Architecture Overview

### Request Flow
1. **Entry Point**: All requests start at `index.php` (login) or authenticated pages
2. **Initialization**: `php/init.php` handles:
   - Environment configuration loading
   - Database connection setup
   - Authentication checks
   - Language/localization setup
   - CSRF protection initialization
3. **Routing**: Direct file access (no routing framework) - each module has its own PHP files
4. **Response**: Standardized JSON responses via `_response()` helper

### Form Submission Pattern
All forms use AJAX submission with a unified handler:

```javascript
// Forms with class="ajax" are automatically handled
<form method="post" class="ajax">
    <!-- form fields -->
    <button type="submit">Submit</button>
</form>
```

The AJAX handler (in `inc/scripts.php`):
- Automatically adds CSRF tokens
- Handles file uploads via FormData
- Processes JSON responses
- Executes response actions (redirect, reload, etc.)
- Shows notifications via PNotify/SweetAlert2

### Response Pattern
All PHP endpoints return standardized JSON responses:

```php
return _response([
    'success' => true/false,
    'message' => ['Success message'] // Array of messages
    'action' => 'redirect:' . base_url('path/to/page.php'),
    'data' => [] // Optional data payload
]);
```

### Database Architecture
- **ORM**: Illuminate Database (Eloquent) - Models in `php/Modals/`
- **Raw Queries**: PDO wrapper class for complex queries
- **Key Models**:
  - `Product`: Inventory items with branch quantities
  - `Sale`/`Purchase`: Transaction records
  - `Customer`/`Supplier`: Contact management
  - `User`: Authentication and permissions

### Authentication Flow
1. Login form submission to `index.php` (class="ajax")
2. `Auth::login()` validates credentials
3. Session created with user ID
4. Optional "Remember Me" cookie
5. Redirects blocked to login if not authenticated

### Module Structure
Each business module follows this pattern:
```
module_name/
├── index.php      # List/grid view
├── create.php     # Create new record
├── update.php     # Edit existing record
├── view.php       # Detail view (optional)
└── functions.php  # Module-specific functions
```

### UI/UX Patterns
- **DataTables**: Used for all data grids (class="datatable")
- **Select2**: Enhanced dropdowns (class="select2")
- **Notifications**: PNotify for inline, SweetAlert2 for modals
- **Forms**: Uniform styling with Bootstrap classes
- **Loading States**: Automatic button disable/spinner on submit

## Key Helper Functions

```php
// Input handling
input('field_name')       // Get POST/GET input safely
old('field_name')         // Get previous form input

// URL generation
base_url('path/file.php') // Generate full URL
url()->get()              // Get current URL

// Authentication
auth()                    // Get current user or false
branch()                  // Get current branch context
can('permission')         // Check user permission

// Localization
tr('Text to translate')   // Translate string
_local()                  // Get locale attributes (dir/lang)

// Response handling
_response($data)          // Send JSON response
display()->messages()     // Show flash messages
```

## Critical Patterns to Follow

1. **Always Check Authentication**:
   ```php
   if (!auth()) {
       redirect()->to(base_url('index.php'));
   }
   ```

2. **Use Transactions for Data Integrity**:
   ```php
   DB::beginTransaction();
   try {
       // Multiple operations
       DB::commit();
   } catch (Exception $e) {
       DB::rollback();
   }
   ```

3. **Validate All Input**:
   ```php
   $v = validate([
       'Field Label' => ['field_name', 'required|numeric']
   ]);
   if (!$v->passes()) {
       return _response(['success' => false, 'message' => $v->errors()->all()]);
   }
   ```

4. **Handle File Uploads**:
   ```php
   if (input_file('image')) {
       $image_path = file_upload('image', 'uploads/', ['jpg','png']);
   }
   ```

## Storage Library

The enhanced Storage library (`php/Libraries/Storage.php`) provides secure file upload handling with backward compatibility:

### Basic Usage
```php
use App\Libraries\Storage;

$storage = new Storage();
$result = $storage->save($_FILES['file']);

if ($result->saved()) {
    $fileId = $result->id;
    $fileUrl = $storage->get($fileId);
}
```

### Advanced Features

**1. Thumbnail Generation for Images**:
```php
$storage->enableThumbnails(true, [
    'thumb' => ['width' => 150, 'height' => 150],
    'medium' => ['width' => 500, 'height' => 500]
]);

$result = $storage->save($_FILES['image']);

// Get thumbnail URLs
$thumbUrl = $storage->get($result->id, 'thumb');
$mediumUrl = $storage->get($result->id, 'medium');
```

**2. Multiple File Upload**:
```php
$results = $storage->saveMultiple($_FILES['files']);
foreach ($results as $file) {
    if ($file['status']) {
        echo "Uploaded: " . $file['id'];
    }
}
```

**3. File Type Restrictions**:
```php
$storage->setAllowedTypes([
    'image/jpeg' => ['jpg', 'jpeg'],
    'image/png' => ['png'],
    'application/pdf' => ['pdf']
]);
```

**4. File Information & Deletion**:
```php
// Get detailed file info
$info = $storage->getFileInfo($fileId);
// Returns: id, name, size, mime_type, url, thumbnails, etc.

// Delete file and thumbnails
$storage->delete($fileId);
```

### Storage Features
- **Security**: MIME type validation, file integrity checks
- **Organization**: Files stored in year/month subdirectories
- **Thumbnails**: Automatic generation with aspect ratio preservation
- **Logging**: Operation tracking for uploads/deletions
- **Backward Compatible**: Old `file_upload()` helper still works

### Database Schema
Files are stored in the `uploads` table with enhanced fields:
- `size` - File size in bytes
- `mime_type` - Verified MIME type
- `extension` - File extension
- `created_by` - User who uploaded

### Migration
Run `database/migrations/add_storage_enhancements.sql` to add new columns to existing installations.

## Common Gotchas

1. **CSRF Token**: Required header `X-Cs-332598` on all AJAX requests (handled automatically by form handler)
2. **Branch Context**: Many operations are branch-specific - always consider `branch()` in queries
3. **Language Direction**: Support RTL/LTR - use `_local()` for dir attribute
4. **Timezone**: Set to 'Asia/Muscat' - consider for date operations
5. **Session Timeout**: Extended to ~20 hours (74000 seconds)

## Security Considerations

1. All forms include CSRF protection
2. Input sanitization via `input()` helper
3. Password hashing with `password_hash()`
4. SQL injection prevention via parameterized queries
5. XSS prevention with `escap()` helper for output

## Performance Tips

1. DataTables use server-side processing for large datasets
2. Use eager loading for Eloquent relationships
3. Implement caching for frequently accessed data
4. Optimize images on upload
5. Use pagination for large result sets

## Page Layout Examples

### Basic Page Structure
All pages follow a consistent layout pattern using `_layout_start()` and `_layout_end()` functions:

```php
<?php
require(__DIR__."/../php/init.php");

// Page configuration
$page = [
    "title" => tr("Page Title"),
    "active" => "module_name",     // For sidebar highlighting
    "class" => "sidebar-xs"         // Additional body classes
];

// Navigation configuration
$page_nav = [
    "back" => base_url("module"),   // Back button URL
    "reload" => 1,                   // Show reload button
    "breadcrumb" => [
        tr("Home") => base_url("dashboard.php"),
        tr("Module") => base_url("module"),
        tr("Current Page") => ""
    ]
];

// Check permissions
if (!can("permission-name")) {
    die("403");
}
?>

<?php _layout_start() ?>

<!-- Page content goes here -->
<div class="card">
    <div class="card-body">
        <!-- Your content -->
    </div>
</div>

<?php _layout_end() ?>
```

### Form Page Example (Create/Update)
```php
<?php
// Handle form submission
if (is_post()) {
    // Validation
    $v = validate([
        tr("Field Label") => ["field_name", "required|rules"],
        // More validation rules
    ]);
    
    if (!$v->passes()) {
        _response([
            "success" => false,
            "message" => $v->errors()->all()
        ]);
    }
    
    // Process form data
    // Save to database
    
    // Return success response
    _response([
        "success" => true,
        "message" => [tr("Created successfully")],
        "action" => "redirect:" . base_url("module/view.php?i=$id")
    ]);
}
?>

<?php _layout_start() ?>

<form class="ajax" method="post">
    <div class="card">
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <label><?= tr("Field Name") ?></label>
                    <input type="text" name="field_name" class="form-control" 
                           value="<?= old('field_name', $model->field_name ?? '') ?>">
                </div>
            </div>
        </div>
        <div class="card-footer">
            <button type="submit" class="btn btn-primary"><?= tr("Submit") ?></button>
        </div>
    </div>
</form>

<?php _layout_end() ?>
```

### List/Index Page Example
```php
<?php _layout_start() ?>

<div class="card">
    <div class="card-header">
        <h5 class="card-title"><?= tr("Items List") ?></h5>
        <div class="header-elements">
            <a href="create.php" class="btn btn-primary btn-sm">
                <i class="icon-plus3"></i> <?= tr("Create New") ?>
            </a>
        </div>
    </div>
    
    <table class="table datatable">
        <thead>
            <tr>
                <th><?= tr("ID") ?></th>
                <th><?= tr("Name") ?></th>
                <th><?= tr("Status") ?></th>
                <th class="text-center"><?= tr("Actions") ?></th>
            </tr>
        </thead>
        <tbody>
            <?php foreach ($items as $item): ?>
            <tr>
                <td><?= $item->id ?></td>
                <td><?= escap($item->name) ?></td>
                <td><?= $item->status ?></td>
                <td class="text-center">
                    <a href="update.php?i=<?= $item->id ?>" class="btn btn-sm btn-primary">
                        <i class="icon-pencil"></i>
                    </a>
                </td>
            </tr>
            <?php endforeach ?>
        </tbody>
    </table>
</div>

<?php _layout_end() ?>
```

### Vue.js Integration Example
For complex forms with dynamic elements:

```php
<?php _layout_start() ?>

<div id="vueapp">
    <form class="ajax" method="post">
        <div class="card">
            <!-- Vue-controlled content -->
            <table class="table">
                <tbody>
                    <tr v-for="(item, index) in items">
                        <td>{{item.name}}</td>
                        <td>
                            <input type="hidden" :name="'items[' + index + '][id]'" :value="item.id">
                            <input type="number" :name="'items[' + index + '][quantity]'" 
                                   v-model="item.quantity" class="form-control">
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </form>
</div>

<script>
new Vue({
    el: '#vueapp',
    data: {
        items: <?= json_encode($items) ?>
    },
    methods: {
        // Vue methods
    }
});
</script>

<?php _layout_end() ?>
```

### Layout Types
- **Default Layout**: `_layout_start()` / `_layout_end()` - Standard page with header, sidebar, footer
- **Auth Layout**: `_layout_start("auth")` / `_layout_end("auth")` - Clean layout for login/register pages
- **Custom Layouts**: Place in `layout/[name]/` directory and call with layout name

### Common Page Variables
- `$page['title']`: Sets the page title in browser tab
- `$page['active']`: Highlights active menu item in sidebar
- `$page['class']`: Additional CSS classes for body tag (e.g., "sidebar-xs" for collapsed sidebar)
- `$page_nav['breadcrumb']`: Array of breadcrumb items
- `$page_nav['back']`: URL for back button in header
- `$page_nav['reload']`: Show/hide reload button (1 or 0)

## Settings System

The application uses a flexible settings system with two types of settings:

### 1. Global Settings (Options Table)
Stored in the `options` table and accessed via helper functions:

```php
// Get a setting value
$value = get_option('setting_name', 'default_value');

// Set a setting value
set_option('setting_name', 'new_value');

// Common usage examples
$currency = get_option('currency_symbol', 'OMR');
$tax_included = get_option('price_include_tax', '0');
$decimal_points = get_option('float_points', 3);
```

### 2. Branch-Specific Settings
Stored as JSON in the `branches.options` column:

```php
// Branch model has 'options' cast as array
$branch = Branch::find(1);
$branch->options['receipt_header'] = 'Custom header text';
$branch->save();

// Access branch options
$header = $branch->options['receipt_header'] ?? '';
```

### Settings Categories Structure

The settings page (`settings/index.php`) organizes settings into categories:

```php
$categories = [
    'business' => [
        'title' => 'Business Settings',
        'icon' => 'icon-cog',
        'sections' => [
            [
                'title' => 'General',
                'settings' => [
                    'setting_key' => [
                        'type' => 'text|select|checkbox|textarea|file|etc',
                        'label' => 'Display Label',
                        'default' => 'default_value',
                        'options' => [...], // For select type
                        'col' => 'col-md-6', // Bootstrap column width
                        'help' => 'Help text'
                    ]
                ]
            ]
        ]
    ]
];
```

### Common Settings Used Throughout the Application

**Pricing & Currency:**
- `price_include_tax` - Whether prices include tax (0/1)
- `currency_symbol` - Currency symbol (e.g., "OMR")
- `currency_position` - Symbol position (left/right)
- `float_points` - Decimal places for prices
- `number_separator` - Thousands separator

**POS Settings:**
- `pos_shortcut_*` - Keyboard shortcuts
- `pos_disable_*` - Feature toggles
- `pos_auto_add_product_on_search` - Auto-add behavior

**Printing:**
- `print_receipt_mode` - Receipt printing enabled/disabled
- `print_barcode_mode` - Barcode printing settings
- `barcode_type` - Barcode format (C128, EAN13, etc.)

**Business Info:**
- `app_name` - Application name
- `business_name` - Business display name
- `logo` - Logo file ID (stored via storage system)

### Adding New Settings

1. **Add to settings page** - Update the `$categories` array in `settings/index.php`
2. **Use in code** - Access with `get_option('key', 'default')`
3. **Branch-specific** - Add to `settings/branch.php` for per-branch settings

### Settings Storage Flow

1. Form submission via AJAX (class="ajax")
2. Validation based on setting type
3. Special handling for file uploads (stored via storage system)
4. Settings saved to database
5. Response triggers page reload to reflect changes

### Important Notes

- Settings are cached in PHP session - changes require page reload
- File uploads store the file ID, not the path
- Branch settings allow customization per location
- All settings have default values to prevent errors
- Settings page requires 'settings' permission