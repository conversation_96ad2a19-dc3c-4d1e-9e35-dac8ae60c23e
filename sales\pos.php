<?php 
use Illuminate\Database\Capsule\Manager as DB;

use App\Models\Customer;
use App\Models\CustomerGroup;
use App\Models\Branch;
use App\Models\Tax;
use App\Models\ProductLine;
use App\Models\Sale;
use App\Models\Payment;
use App\Models\Product;
use App\Models\Category;

require(__DIR__."/../php/init.php");

$page = [
    "title" => tr("Point of Sale"),
    "active" => "pos",
    "class" => "sidebar-xs",
];

if (!can("create-sales")) {
    die("403");
}

$branches = Branch::all();


    
    
$products = Product::with(['brandCategory', 'category'])
    ->select('products.*')
    ->leftJoin('product_line', 'products.id', '=', 'product_line.product_id')
    ->selectRaw('COUNT(product_line.id) as sales_count')
    ->groupBy('products.id')
    ->orderBy('sales_count', 'desc')
    ->limit(200)->get();

// Enhance the products collection with additional data
$products = $products->map(function ($product) {
    return [
        'id' => $product->id,
        'name' => $product->name,
        'name2' => $product->name2,
        'code' => $product->code,
        'description' => $product->description,
        'price' => $product->price,
        'before_price' => $product->price,
        'price_include_tax' => $product->price_include_tax,
        'min_price' => $product->min_price,
        'tax_percent' => $product->tax->percent??0,
        'cost' => $product->cost,
        
        'unit_id' => $product->unit_id,
        'brand_name' => $product->brand ? $product->brand->name : 'No Brand',
        'category_name' => $product->category ? $product->category->name : 'No Category',
        'branch_quantity' => _float($product->branchQuantity(_branch()->id()),0),

        'data' => $product,
    ];
});
    


$categories = Category::where("type","products")->get();
$brands = Category::where("type","brand")->get();

// Get tax rates
$taxes = Tax::all();

$page_nav = [
    // "reload" => 1,
    // "back" => base_url("sales"),
  
];

?>

<?php _layout_start() ?>

<!-- Custom CSS for POS -->
<style>
.product-item {
    cursor: pointer;
    transition: all 0.2s ease;
    border-radius: 0;
    border: 1px solid #888;
}

.product-item:hover {
    transform: translateY(-3px);
    /* box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1); */
}

.cart-items {
    max-height: calc(100vh - 400px);
    overflow-y: auto;
}

.cart-total {
    position: sticky;
    bottom: 0;
    background: #fff;
    border-top: 1px solid #ddd;
    z-index: 10;
}

.category-tabs {
    overflow-x: auto;
    white-space: nowrap;
    flex-wrap: nowrap;
}

.product-search {
    position: sticky;
    top: 0;
    z-index: 100;
    background: #fff;
    padding: 15px 0;
    border-top-left-radius: var(--card-border-radius);
    border-top-right-radius: var(--card-border-radius);
}

.payment-methods .btn {
    margin-bottom: 10px;
}

/* Barcode Scanner Styles */
.barcode-scanner-section {
    position: sticky;
    top: 0;
    z-index: 100;
    background: #fff;
}

.barcode-notification {
    animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Loading state for barcode input */
.barcode-loading {
    opacity: 0.7;
    pointer-events: none;
}

@media (max-width: 767px) {
    .mobile-cart-toggle {
        display: block;
        margin-bottom: 15px;
    }

    .pos-cart {
        display: none;
    }

    .pos-cart.show {
        display: block;
    }

    .product-list {
        display: none;
    }

    .product-list.show {
        display: block;
    }

    .cart-items {
        max-height: 300px;
    }
}
</style>

<div class="container-fluid mt-1" id="pos-app">

    <div>
        <!-- POS Action Buttons -->
        <div class="pos-action-buttons mb-0">
            <div class="d-flex flex-wrap ">

                <div class="btn-group mr-2 mb-2">
                    <div class="dropdown">
                        <button class="btn btn-secondary dropdown-toggle" type="button" id="branchDropdown" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            <?= _branch()->name() ?>
                        </button>
                        <div class="dropdown-menu" aria-labelledby="branchDropdown">
                            <?php foreach ($branches as $branch): ?>
                            <a class="dropdown-item" href="<?= url()->add("branch", $branch->id) ?>"><?= $branch->name ?></a>
                            <?php endforeach ?>
                        </div>
                    </div>
                </div>
                <div class="btn-group mr-2 mb-2">
                    <button class="btn btn-sm btn-primary" @click="toggleFullScreen()">
                        <i class="fa fa-expand"></i> <?= tr("Full Screen") ?>
                    </button>
                    <button class="btn btn-sm btn-primary" onclick="window.open(window.location.href, '_blank')">
                        <i class="fa fa-external-link-alt"></i> <?= tr("New Tab") ?>
                    </button>
                </div>

                <div class="btn-group mr-2 mb-2">
                    <button class="btn btn-sm btn-secondary" data-toggle="modal" data-target="#addExpenseModal">
                        <i class="fa fa-minus-circle"></i> <?= tr("Add Expense") ?>
                    </button>
                </div>

                <div class="btn-group mb-2">
                    <button class="btn btn-sm btn-success" data-toggle="modal" data-target="#addPaymentModal">
                        <i class="fa fa-money-bill-wave"></i> <?= tr("Add Payment") ?>
                    </button>
                </div>
            </div>
        </div>
    </div>


    <!-- Mobile cart toggle button (visible only on small screens) -->
    <button class="btn btn-primary btn-block mobile-cart-toggle d-md-none" onclick="toggleCart()">
        <i class="fa fa-shopping-cart mr-2"></i> <?= tr("Toggle Cart") ?> <span
            class="badge badge-light ml-1">{{cartItems.length}}</span>
    </button>

    <div class="row">
        <!-- Products Section -->
        <div class="col-lg-8 col-md-7 mb-4 product-list show">
            <?php include(__DIR__."/inc/_pos_products.php") ?>
        </div>

        <!-- Cart Section -->
        <div class="col-lg-4 col-md-5 pos-cart">
            <?php include(__DIR__."/inc/_pos_order.php") ?>
        </div>
    </div>

    <?php include(__DIR__."/inc/_pos_product_modal.php") ?>

    <!-- Payment Modal -->
    <?php include(__DIR__."/inc/_pos_payment_modal.php") ?>
    
    <!-- Print Options Modal -->
    <div class="modal fade" id="printOptionsModal" tabindex="-1" role="dialog" aria-labelledby="printOptionsModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="printOptionsModalLabel"><?= tr("Print Options") ?></h5>
                    <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <p class="mb-3"><?= tr("Sale completed successfully. Choose print option:") ?></p>
                    <div class="list-group">
                        <a href="#" class="list-group-item list-group-item-action" @click.prevent="printReceipt(currentSaleId)">
                            <i class="fa fa-receipt mr-2"></i>
                            <strong><?= tr("Receipt") ?></strong>
                            <small class="d-block text-muted"><?= tr("Thermal/POS printer format") ?></small>
                        </a>
                        <a href="#" class="list-group-item list-group-item-action" @click.prevent="printInvoiceA4(currentSaleId)">
                            <i class="fa fa-file-invoice mr-2"></i>
                            <strong><?= tr("A4 Invoice") ?></strong>
                            <small class="d-block text-muted"><?= tr("Full page invoice format") ?></small>
                        </a>
                        <a href="#" class="list-group-item list-group-item-action" @click.prevent="printDeliveryNote(currentSaleId)">
                            <i class="fa fa-truck mr-2"></i>
                            <strong><?= tr("Delivery Note") ?></strong>
                            <small class="d-block text-muted"><?= tr("Delivery/packing slip format") ?></small>
                        </a>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal" @click="clearCart"><?= tr("Close & New Sale") ?></button>
                    <button type="button" class="btn btn-primary" @click="viewSale(currentSaleId)">
                        <i class="fa fa-eye mr-1"></i> <?= tr("View Sale") ?>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>





<script>



let posApp = new Vue({
        el: '#pos-app',
        data: {
            products: <?= json_encode($products) ?>,
            filteredProducts: <?= json_encode($products) ?>,
            currentSaleId: null,
            
            categories: [
                // Sample categories - this should be replaced with actual categories from database
                <?php foreach ($categories as $category): ?> {
                    id: <?= $category->id ?>,
                    name: '<?= $category->name ?>'
                },
                <?php endforeach; ?>
            ],
            activeCategory: 'all',
            searchQuery: '',
            isSearching: false,
            searchTimeout: null,
            isScanning: false,
            customer: {
                id: 0,
                name: '',
                phone: '',
                address: '',
                tax_no: '',
                credit: 0,
                created_at: '',
                updated_at: '',
                deleted_at: '',
                group_id: 0,
                email: '',
                credit_limit: 0,
                credit_balance: 0,
                group: {
                    id: 0,
                    name: '',
                    credit_limit: 0
                }
            },
            cartItems: [],
            selectedCustomer: 0,
            taxRate: 5, // Default tax rate
            discount: 0,
            paymentMethod: 'cash',
            amountTendered: 0,
            price_include_tax: <?= get_option("price_include_tax",false)?'true':'false' ?>,
            discount_before_tax: <?= get_option('discount_before_tax', 0) ?>,
            selected_item: null,
            selected_item_data: null,

            // New payment fields
            payments: [],
            currentPayment: {
                method: 'cash',
                amount: 0,
                reference: ''
            },
            customerNote: '',
            adminNote: '',
            saleData: null,
            
            
            sendReceipt: false
        },
        computed: {
            subtotal() {
                let job = this;
                let subtotal = 0;

                for (let i = 0; i < job.cartItems.length; i++) {
                    let item = job.cartItems[i];
                    let item_price = parseFloat(item.price);
                    let item_quantity = parseFloat(item.quantity);

                    if (0) {
                        // If price includes tax, extract the base price (without tax)
                        let item_tax_percent = parseFloat(item.tax_percent);
                        let taxRate = item_tax_percent / 100;
                        let basePrice = item_price / (1 + taxRate); // Base price without tax
                        subtotal += basePrice * item_quantity;
                    } else {
                        // If price does not include tax, use the price directly
                        subtotal += item_price * item_quantity;
                    }
                }

                return parseFloat(subtotal);
            },
            total_tax() {
                let job = this;
                let totalTax = 0;

                for (let i = 0; i < job.cartItems.length; i++) {
                    let item = job.cartItems[i];
                    let item_price = parseFloat(item.price);
                    let item_quantity = parseFloat(item.quantity);
                    let item_tax_percent = parseFloat(item.tax_percent);

                    if (0) {
                        // If price includes tax, extract the tax portion from the price
                        let taxRate = item_tax_percent / 100;
                        let basePrice = item_price / (1 + taxRate); // Extract the base price before tax
                        let taxAmount = item_price - basePrice; // The difference is the tax amount
                        totalTax += taxAmount * item_quantity;
                    } else {
                        // If price does not include tax, calculate tax normally
                        let taxAmount = (item_tax_percent * item_price / 100);
                        totalTax += taxAmount * item_quantity;
                    }
                }

                return parseFloat(totalTax);
            },

            total_price() {
                let job = this;
                let subtotal = 0;

                for (let i = 0; i < job.cartItems.length; i++) {
                    subtotal += job.line_total(i);
                }

                // Subtract the discount from the subtotal
                let total = subtotal - parseFloat(job.discount);

                return parseFloat(total);
            },

            // Payment-related computed properties
            totalPaid() {
                total = 0;
                total += parseFloat(this.currentPayment.amount);
                total += this.payments.reduce((total, payment) => total + parseFloat(payment.amount ||
                    0), 0);
                return total;
            },
            totalRemaining() {
                const remaining = parseFloat(this.total_price) - parseFloat(this.totalPaid);
                return remaining > 0 ? this._float(remaining) : this._float(remaining * -1);
            },
            totalExtract() {
                const remaining = this.total_price - this.totalPaid;
                return remaining > 0 ? this._float(remaining) : 0;
            },
            isFullyPaid() {
                return this.totalPaid >= this.total_price;
            },
            isValidPayment() {
                return parseFloat(this.currentPayment.amount) > 0;
            },
            canCompleteSale() {
                // Sale can be completed if fully paid or credit payment is used
                return this.isFullyPaid || this.payments.some(p => p.method === 'credit');
            }
        },
        methods: {
            line_total(index) {
                let job = this;
                let item = job.cartItems[index];
                let item_price = parseFloat(item.price);
                let item_quantity = parseFloat(item.quantity);
                let item_tax_percent = parseFloat(item.tax_percent);

                let line_total;

                if (0) {
                    // Price already includes tax
                    line_total = item_price * item_quantity;
                } else {
                    // Price does not include tax
                    let item_tax = (item_tax_percent * item_price / 100);
                    line_total = (item_price + item_tax) * item_quantity;
                }

                if (job.discount_before_tax) {
                    // Apply discount before calculating tax
                    line_total -= (parseFloat(job.discount) / job.subtotal) * line_total;
                }

                return line_total;
            },
            update_line(index) {
                this.selected_item = index;
                this.selected_item_data = {
                    ...this.cartItems[index]
                };
                $('#item-line-modal').modal('show')
            },
            setCategory(categoryId) {
                this.activeCategory = categoryId;
                this.filterProductsByCategory();
            },
            filterProductsByCategory() {
                if (this.activeCategory === 'all') {
                    this.filteredProducts = this.products;
                } else {
                    this.filteredProducts = this.products.filter(product => 
                        product.category_id == this.activeCategory
                    );
                }
            },
            customer_get(id) {
                if(!id) return;

                let job = this;
                $.get('<?= base_url("customers/functions.php?f=get") ?>&id=' + id, function(response){
                    if (response.success) {
                        job.customer = response.data;
                    }
                },'json');
            },
            searchProducts() {
                if (this.searchQuery === '') {
                    this.filteredProducts = this.products;
                    return;
                }

                // Debounce the search to avoid too many AJAX calls
                clearTimeout(this.searchTimeout);
                this.searchTimeout = setTimeout(() => {
                    this.isSearching = true;
                    let job = this;

                    $.ajax({
                        url: '<?= base_url("products/functions.php?f=search") ?>',
                        method: 'POST',
                        dataType: 'json',
                        data: {
                            search: this.searchQuery,
                            branch_id: <?= _branch()->id() ?>
                        },
                        success: function(response) {
                            job.isSearching = false;
                            if (response.success) {
                                job.filteredProducts = response.data;
                            } else {
                                job.filteredProducts = [];
                            }
                        },
                        error: function() {
                            job.isSearching = false;
                            job.filteredProducts = [];
                        }
                    });
                }, 300); // 300ms delay
            },
            resetSearch() {
                clearTimeout(this.searchTimeout);
                this.searchQuery = '';
                this.activeCategory = 'all';
                this.filteredProducts = this.products;
                this.isSearching = false;
            },

            // Barcode Scanner Methods
            handleBarcodeScanned() {
                if (this.searchQuery.trim() === '') return;
                this.searchByBarcode();
            },
            handleBarcodeInput() {
                // Clear previous timeout
                clearTimeout(this.searchTimeout);
                
                // Auto-search after a short delay to handle fast barcode scanners
                this.searchTimeout = setTimeout(() => {
                    if (this.searchQuery.trim() !== '') {
                        this.searchByBarcode();
                    }
                }, 100);
            },
            searchByBarcode() {
                if (this.searchQuery.trim() === '') return;
                
                this.isScanning = true;
                let job = this;
                
                $.ajax({
                    url: '<?= base_url("products/functions.php?f=search") ?>',
                    method: 'POST',
                    dataType: 'json',
                    data: {
                        search: this.searchQuery.trim(),
                        branch_id: <?= _branch()->id() ?>
                    },
                    success: function(response) {
                        job.isScanning = false;
                        if (response.success && response.data.length > 0) {
                            // Look for exact code match first
                            let exactMatch = response.data.find(product => 
                                product.code === job.searchQuery.trim()
                            );
                            
                            // Use exact match if found, otherwise use first result
                            let product = exactMatch || response.data[0];
                            
                            job.addToCart(product);
                            job.clearBarcode();
                            
                            // Show success notification
                            job.showBarcodeNotification('success', 
                                '<?= tr("Product added to cart") ?>: ' + product.name);
                        } else {
                            // Product not found
                            job.showBarcodeNotification('error', 
                                '<?= tr("Product not found") ?>: ' + job.searchQuery);
                        }
                    },
                    error: function() {
                        job.isScanning = false;
                        job.showBarcodeNotification('error', 
                            '<?= tr("Error searching for product") ?>');
                    }
                });
            },
            clearBarcode() {
                clearTimeout(this.searchTimeout);
                this.searchQuery = '';
                this.isScanning = false;
                
                // Refocus the barcode input for continuous scanning
                this.$nextTick(() => {
                    if (this.$refs.searchQuery) {
                        this.$refs.searchQuery.focus();
                    }
                });
            },
            showBarcodeNotification(type, message) {
                // Create a temporary notification element
                const notification = document.createElement('div');
                notification.className = `alert alert-${type === 'success' ? 'success' : 'danger'} 
                                         position-fixed barcode-notification`;
                notification.style.cssText = `
                    top: 20px; 
                    right: 20px; 
                    z-index: 9999; 
                    min-width: 300px;
                    animation: slideInRight 0.3s ease-out;
                `;
                notification.innerHTML = `
                    <div class="d-flex align-items-center">
                        <i class="fa fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'} mr-2"></i>
                        <span>${message}</span>
                    </div>
                `;
                
                document.body.appendChild(notification);
                
                // Auto-remove after 3 seconds
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 3000);
            },
            addToCart(product) {
                const existingItemIndex = this.cartItems.findIndex(item => item.product_id === product.id);
                
                if (existingItemIndex >= 0) {
                    this.cartItems[existingItemIndex].quantity++;
                } else {
                    this.cartItems.push({
                        product_id: product.id,
                        name: product.name,
                        price: product.price,
                        price_include_tax: product.price_include_tax,
                        quantity: 1,
                        tax_percent: product.tax_percent,
                        description: '',
                        unit: '',
                        line_order: this.cartItems.length,
                        before_price: product.before_price,
                        product_type: product.data.type,
                        serial_no: "",
                
                    
                    });
                }
                this.updateAmountTendered();
            },
            removeItem(index) {
                this.cartItems.splice(index, 1);
                this.updateAmountTendered();
            },
            incrementQuantity(index) {
                this.cartItems[index].quantity++;
                this.updateAmountTendered();
            },
            decrementQuantity(index) {
                if (this.cartItems[index].quantity > 1) {
                    this.cartItems[index].quantity--;
                    this.updateAmountTendered();
                }
            },
            updateOriginalPrice(item) {
                if (this.price_include_tax) {
                    // Calculate the base price before tax
                    const taxRate = parseFloat(item.tax_percent) / 100;
                    item.price = item.price_include_tax / (1 + taxRate);
                    console.log(`Original price for item is ${item.price}`);
                } else {
                    // If price does not include tax, the original price is the current price
                    item.price = item.price;
                }
            },
            calculateChange() {
                if (this.paymentMethod !== 'cash') return 0;

                const amount = parseFloat(this.currentPayment.amount || 0);
                const remaining = this.total_price - this.totalPaid;

                return amount > remaining ? (amount - remaining) : 0;
            },
            updateAmountTendered() {
                if (this.paymentMethod === 'cash') {
                    this.amountTendered = this.total_price;
                }
            },
            processSale() {
                if (this.cartItems.length === 0) {
                    alert('<?= tr("Please add items to the cart") ?>');
                    return;
                }
                // Show payment modal to complete the transaction
                this.resetPaymentForm();
                $('#paymentModal').modal('show');

                // Prepare sale data
                this.saleData = {
                    customer_id: this.selectedCustomer,
                    items: this.cartItems,
                    subtotal: this.subtotal,
                    tax: this.total_tax,
                    discount: this.discount,
                    total: this.total_price,
                    payment_method: this.paymentMethod
                };

                // Set default amount to total
                this.currentPayment.amount = this._float(this.total_price);

                // Focus on cash amount input if it exists
                this.$nextTick(() => {
                    if (this.$refs.cashAmount) {
                        this.$refs.cashAmount.focus();
                        this.$refs.cashAmount.select();
                    }
                });
            },
            resetPaymentForm() {
                this.payments = [];
                this.currentPayment = {
                    method: 'cash',
                    amount: 0,
                    reference: ''
                };
                this.customerNote = '';
                this.adminNote = '';
            },
            quickCash(amount) {
                this.currentPayment.amount = parseFloat(amount);
            },
            addPayment() {
                if (!this.isValidPayment) return;

                // Add payment to list
                this.payments.push({
                    method: this.paymentMethod,
                    amount: parseFloat(this.currentPayment.amount),
                    reference: this.currentPayment.reference
                });

                // Reset current payment data except method
                this.currentPayment.amount = this.totalRemaining > 0 ? this.totalRemaining : 0;
                this.currentPayment.reference = '';
            },
            removePayment(index) {
                this.payments.splice(index, 1);

                // Update current payment amount to remaining
                if (this.totalRemaining > 0) {
                    this.currentPayment.amount = this.totalRemaining;
                }
            },
            completeSale() {
                // Validate required fields
                if (this.cartItems.length === 0) {
                    alert('<?= tr("Please add items to the cart") ?>');
                    return;
                }

                if (this.totalPaid < this.total_price) {
                    alert('<?= tr("Payment amount is less than the total") ?>');
                    return;
                }

                var payments = [...this.payments];
                payments.push(this.currentPayment);

                // Prepare sale data
                const finalSaleData = {
                    customer_id: this.selectedCustomer || 0,
                    branch_id: <?= _branch()->id() ?>,
                    date: new Date().toISOString().split('T')[0],
                    discount: this.discount,
                    invoice_total_price: this.total_price,
                    invoice_total_paid: this.totalPaid,
                    payments: payments,
                    items: this.cartItems.map(item => ({
                        ...item,
                        tax_percent: parseFloat(item.tax_percent),
                        price: parseFloat(item.price),
                        quantity: parseFloat(item.quantity)
                    })),
                    status: 'delivered',
                    customer_note: this.customerNote,
                    admin_note: this.adminNote,
                    send_receipt: this.sendReceipt
                };

                // Send sale data to server
                $.ajax({
                    url: '<?= base_url("sales/pos_functions.php?f=create_order") ?>',
                    method: 'POST',
                    dataType: 'json',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    data: JSON.stringify(finalSaleData),
                    success: function(data) {
                        if (data.success) {
                            // Hide payment modal
                            $('#paymentModal').modal('hide');

                            // Get the sale ID from response
                            const saleId = (data.data && data.data.id) || data.sale_id;
                            
                            if (saleId) {
                                // Store the sale ID
                                posApp.currentSaleId = saleId;
                                
                                // Show print options modal
                                $('#printOptionsModal').modal('show');
                                
                                // Auto-print receipt if setting is enabled
                                if (<?= get_option('pos_auto_print_receipt', '1') ?> == 1) {
                                    posApp.printReceipt(saleId);
                                }
                            } else {
                                alert('<?= tr("Sale completed but could not open receipt") ?>');
                            }
                            
                            // If there's an action to perform
                            if (data.action) {
                                eval(data.action);
                            }
                        } else {
                            alert(data.message || '<?= tr("Error processing sale") ?>');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('Error:', error);
                        console.error('Response:', xhr.responseText);
                        alert('<?= tr("Error processing sale") ?>');
                    }
                });
            },
            toggleFullScreen() {
                if (!document.fullscreenElement) {
                    document.documentElement.requestFullscreen();
                } else {
                    if (document.exitFullscreen) {
                        document.exitFullscreen();
                    }
                }
            },
            openCashDrawer() {
                // This function would typically use a receipt printer API or
                // send a command to open the cash drawer
                alert('<?= tr("Cash drawer command sent") ?>');
            },
            clearCart() {
                this.cartItems = [];
                this.discount = 0;
                this.amountTendered = 0;
                this.payments = [];
                this.customerNote = '';
                this.adminNote = '';
                this.currentSaleId = null;
                this.resetPaymentForm();
            },
            printReceipt(saleId) {
                const receiptUrl = '<?= base_url("sales/prints/receipt.php") ?>?i=' + saleId;
                window.open(receiptUrl, '_blank');
            },
            printInvoiceA4(saleId) {
                const invoiceUrl = '<?= base_url("sales/prints/invoice-a4.php") ?>?i=' + saleId;
                window.open(invoiceUrl, '_blank');
            },
            printDeliveryNote(saleId) {
                const deliveryUrl = '<?= base_url("sales/prints/delivery-note.php") ?>?i=' + saleId;
                window.open(deliveryUrl, '_blank');
            },
            viewSale(saleId) {
                window.location.href = '<?= base_url("sales/view.php") ?>?i=' + saleId;
            },
            to_price(val, format = false) {
                let roundedValue = parseFloat(_toNearest(val)).toFixed(
                    <?= get_option("float_points", 3) ?>);
                return format ? _foramt_price(roundedValue) : roundedValue;
            },
            _float(val, dic = <?= get_option("float_points", 3) ?>) {
                let roundedValue = parseFloat(this._toNearest(val)).toFixed(dic);
                return roundedValue;
            },
            _toNearest(num, frac = 0.0001) {
                return Math.ceil(parseFloat(num) / frac) * frac
            },
            update_item_line_save() {
                if (this.selected_item !== null && this.selected_item_data) {
                    // Update the cart item with the modified data
                    this.cartItems[this.selected_item] = {
                        ...this.cartItems[this.selected_item],
                        ...this.selected_item_data
                    };
                    
                    // Close the modal
                    $('#item-line-modal').modal('hide');
                    
                    // Reset selected item
                    this.selected_item = null;
                    this.selected_item_data = null;
                }
            },
            _format_price(n, isShowCurrancy = true) {
                let roundedValue = parseFloat(this._toNearest(n)).toFixed(
                    <?= get_option("float_points", 3) ?>)
                var val = Math.round(Number(roundedValue) * 100) / 100;
                var parts = roundedValue.toString().split(".");
                var num = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g,
                    "<?= get_option("number_separator",",")  ?>") + (parts[1] ?
                    "<?= get_option("decimal_separator",".")  ?>" + parts[1] : "");
                return num + (isShowCurrancy ? " <?= get_option('currency_symbol', 'OMR') ?>" : "");
            },
            
          
        },
        watch: {
            cartItems: {
                handler(newList, oldList) {
                    newList.forEach((item, index) => {
                        this.$watch(
                            () => item.price_include_tax,
                            (newValue, oldValue) => {
                                this.updateOriginalPrice(item);
                            }
                        );
                        this.$watch(
                            () => item.tax_percent,
                            (newValue, oldValue) => {
                                this.updateOriginalPrice(item);
                            }
                        );
                    });
                },
                immediate: true,
                deep: true,
            },
            paymentMethod(newMethod) {
                this.currentPayment.method = newMethod;
            }
        },
        mounted() {
            // Initialize filtered products with all products
            this.filteredProducts = this.products;
            
            // Focus on barcode input for immediate scanning
            this.$nextTick(() => {
                if (this.$refs.barcodeInput) {
                    this.$refs.barcodeInput.focus();
                }
            });
            
            // Add global keyboard shortcuts
            document.addEventListener('keydown', (e) => {
                // F2 key to focus on barcode scanner
                if (e.key === 'F2') {
                    e.preventDefault();
                    if (this.$refs.barcodeInput) {
                        this.$refs.barcodeInput.focus();
                        this.$refs.barcodeInput.select();
                    }
                }
                
                // Escape key to clear barcode
                if (e.key === 'Escape' && document.activeElement === this.$refs.barcodeInput) {
                    e.preventDefault();
                    this.clearBarcode();
                }
            });
            
            document.addEventListener('DOMContentLoaded', function() {
                initAjaxSelect2();
            });
        }
    });

// JavaScript for POS page
document.addEventListener('DOMContentLoaded', function() {
    // Toggle cart on mobile
    window.toggleCart = function() {
        document.querySelector('.pos-cart').classList.toggle('show');
        document.querySelector('.product-list').classList.toggle('show');
    };



    // Initialize Vue app


  
});




</script>

<?php _layout_end() ?>
<!-- </rewritten_file> -->