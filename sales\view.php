<?php 


use Illuminate\Database\Capsule\Manager as DB;

use App\Models\Customer;
use App\Models\CustomerGroup;
use App\Models\Branch;
use App\Models\Tax;
use App\Models\ProductLine;
use App\Models\Sale;
use App\Models\Payment;
use App\Models\Product;
use App\Models\Comment;

require(__DIR__."/../php/init.php");



$sale = Sale::find(input("i"));


if (!$sale) {
    _error_code(404);
}


$items = ProductLine::where("reltype",'sales')->where("relid",$sale->id)->get();
$payments = Payment::where("reltype",'sales')->where("relid",$sale->id)->get();


$page=[
    "title"=>tr("Sales") ."#". $sale->id,
    "active"=>"sales",
    "class"=>" sidebar-xs ",
];

if (!can("manage-sales|create-sales")) {
    die("403");
}


$page_nav=[
    "reload"=>1,
    "back"=>base_url("sales"),
    "breadcrumb"=>[
        tr("Home")=>base_url("dashboard.php"),
        tr("Sales")=>base_url("sales"),
        tr("Sales") ." #". $sale->id=>base_url("sales"),
    ],
];


?>

<?php _layout_start() ?>


<div class="d-flex justify-content-between align-items-center ">
    <div>
        <h3 class=""><?php echo tr("Sale Invoice") ?> #<?php echo $sale->id ?></h3>
       
    </div>
    <div>
        <div class="btn-group" role="group">
            <button type="button" class="btn btn-primary btn-lg dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                <i class="fa fa-print me-2"></i> <?= tr("Print") ?>
            </button>
            <div class="dropdown-menu">
                <a class="dropdown-item" href="<?= base_url("sales/prints/receipt.php?i=" . $sale->id) ?>" target="_blank">
                    <i class="fa fa-receipt mr-2"></i> <?= tr("Receipt") ?>
                </a>
                <a class="dropdown-item" href="<?= base_url("sales/prints/invoice-a4.php?i=" . $sale->id) ?>" target="_blank">
                    <i class="fa fa-file-invoice mr-2"></i> <?= tr("A4 Invoice") ?>
                </a>
                <a class="dropdown-item" href="<?= base_url("sales/prints/delivery-note.php?i=" . $sale->id) ?>" target="_blank">
                    <i class="fa fa-truck mr-2"></i> <?= tr("Delivery Note") ?>
                </a>
            </div>
        </div>
        <a href="<?php echo base_url("sales/update.php",$sale->id) ?>" class="btn btn-primary btn-lg ml-2">
            <i class="fa fa-edit me-2"></i> <?= tr("Edit Invoice") ?>
        </a>
    </div>
</div>

<div class="row g-4">
    <!-- Invoice Details Card -->
    <div class="col-12">
        <div class="card shadow-sm">
            <div class="card-body">
                <div class="row g-4">
                    <div class="col-lg-3 col-md-6">
                        <div class="border rounded-3 p-3 h-100 bg-light">
                            <label class="text-muted small mb-1"><?= tr("Customer") ?></label>
                            <div class="h5 mb-0 fw-bold"><?= $sale->customer->name ?? tr("Walk-in customer") ?></div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <div class="border rounded-3 p-3 h-100 bg-light">
                            <label class="text-muted small mb-1"><?= tr("Branch") ?></label>
                            <div class="h5 mb-0 fw-bold"><?= $sale->branch->name ?></div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <div class="border rounded-3 p-3 h-100 bg-light">
                            <label class="text-muted small mb-1"><?= tr("Invoice date") ?></label>
                            <div class="h5 mb-0 fw-bold"><?= $sale->date ?></div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <div class="border rounded-3 p-3 h-100 bg-light">
                            <label class="text-muted small mb-1"><?= tr("Invoice status") ?></label>
                            <div class="h5 mb-0"><?= _render_sales_status($sale->status) ?></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Items Table -->
            <div class="table-responsive">
                <table class="table table-striped table-hover border-top mb-0">
                    <thead class="bg-primary text-white">
                        <tr>
                            <th class="py-3"><?= tr("No.") ?></th>
                            <th class="py-3"><?= tr("Item") ?></th>
                            <th class="py-3"><?= tr("Description") ?></th>
                            <th class="py-3 text-center text-monospace"><?= tr("Qty") ?></th>
                            <th class="py-3 text-center text-monospace"><?= tr("Price") ?> <?= _price_type_text() ?>
                            </th>
                            <th class="py-3 text-center text-monospace"><?= tr("Tax") ?></th>
                            <th class="py-3 text-center text-monospace"><?= tr("Total") ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($items as $key => $item): ?>
                        <tr>
                            <td class="text-monospace py-3"><?php echo $key + 1 ?></td>
                            <td class="py-3"><strong><?php echo $item->name ?></strong></td>
                            <td class="py-3 text-muted"><?php echo $item->description ?></td>
                            <td class="py-3 text-center number text-monospace"><?php echo $item->quantity ?></td>
                            <td class="py-3 text-center number text-monospace"><?php echo _float($item->price) ?></td>
                            <td class="py-3 text-center number text-monospace"><?php echo $item->tax_percent ?></td>
                            <td class="py-3 text-center number text-monospace">
                                <?php echo _format_price($item->total_price*$item->quantity) ?></td>
                        </tr>
                        <?php endforeach ?>
                    </tbody>

                    <tfoot class="border-top">
                        <tr>
                            <th colspan="6" class="text-end pe-4"><?= tr("Subtotal") ?></th>
                            <td class="text-center text-monospace">
                                <?php echo _format_price($sale->total_price - $sale->total_tax + $sale->discount,1) ?>
                            </td>
                        </tr>
                        <tr>
                            <th colspan="6" class="text-end pe-4"><?= tr("Tax") ?></th>
                            <td class="text-center text-monospace"><?php echo _format_price( $sale->total_tax ,1) ?>
                            </td>
                        </tr>
                        <tr>
                            <th colspan="6" class="text-end pe-4"><?= tr("Discount") ?></th>
                            <td class="text-center text-monospace"><?php echo _format_price( $sale->discount ,1) ?></td>
                        </tr>
                        <tr class="border-top">
                            <th colspan="6" class="text-end pe-4 h5"><?= tr("Total") ?></th>
                            <th class="text-center text-monospace h5">
                                <?php echo _format_price( $sale->total_price ,1) ?></th>
                        </tr>
                        <tr>
                            <th colspan="6" class="text-end pe-4 h5"><?= tr("Paid") ?></th>
                            <th class="text-center text-success text-monospace h5">
                                <?php echo _format_price( $sale->paid ,1) ?></th>
                        </tr>
                        <tr>
                            <th colspan="6" class="text-end pe-4 h5"><?= tr("Balance") ?></th>
                            <th class="text-center text-danger text-monospace h5">
                                <?php echo _format_price($sale->total_price - $sale->paid ,1) ?></th>
                        </tr>
                    </tfoot>
                </table>
            </div>

            <!-- Notes and Attachments -->
            <div class="card-body ">
                <div class="row g-4">
                    <div class="col-md-6">
                        <div class="mb-4">
                            <label class="form-label fw-bold"><?= tr("Customer note") ?></label>
                            <textarea class="form-control bg-white" name="customer_note"
                                rows="3"><?= $sale->customer_note ?></textarea>
                        </div>

                        <div>
                            <label class="form-label fw-bold"><?= tr("Admin note") ?></label>
                            <textarea class="form-control bg-white" name="admin_note"
                                rows="3"><?= $sale->admin_note ?></textarea>
                        </div>
                    </div>


                </div>
            </div>
        </div>
    </div>

    <!-- Payments Table -->
    <div class="col-12">
        <div class="card ">

            <?php echo _payments_table($sale->id,"sales") ?>

        </div>
    </div>
</div>


<?php _layout_end() ?>