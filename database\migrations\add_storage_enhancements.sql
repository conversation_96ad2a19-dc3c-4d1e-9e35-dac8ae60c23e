-- Add new columns to uploads table for enhanced storage functionality
-- This migration adds backward-compatible enhancements to the storage system

-- Add size column if it doesn't exist
ALTER TABLE `uploads` ADD COLUMN IF NOT EXISTS `size` BIGINT NULL AFTER `file`;

-- Add mime_type column if it doesn't exist
ALTER TABLE `uploads` ADD COLUMN IF NOT EXISTS `mime_type` VARCHAR(255) NULL AFTER `size`;

-- Add extension column if it doesn't exist
ALTER TABLE `uploads` ADD COLUMN IF NOT EXISTS `extension` VARCHAR(10) NULL AFTER `mime_type`;

-- Add created_by column if it doesn't exist (for tracking who uploaded the file)
ALTER TABLE `uploads` ADD COLUMN IF NOT EXISTS `created_by` INT(11) NULL AFTER `public`;

-- Add index on created_by for better performance
ALTER TABLE `uploads` ADD INDEX IF NOT EXISTS `idx_created_by` (`created_by`);

-- Add index on mime_type for filtering
ALTER TABLE `uploads` ADD INDEX IF NOT EXISTS `idx_mime_type` (`mime_type`);

-- Add index on created_at for sorting
ALTER TABLE `uploads` ADD INDEX IF NOT EXISTS `idx_created_at` (`created_at`);

-- Update existing records to populate new fields based on file names
UPDATE `uploads` 
SET `extension` = LOWER(SUBSTRING_INDEX(`file`, '.', -1))
WHERE `extension` IS NULL AND `file` LIKE '%.%';

-- Set a default mime type for existing files based on extension
UPDATE `uploads` 
SET `mime_type` = CASE 
    WHEN LOWER(SUBSTRING_INDEX(`file`, '.', -1)) IN ('jpg', 'jpeg') THEN 'image/jpeg'
    WHEN LOWER(SUBSTRING_INDEX(`file`, '.', -1)) = 'png' THEN 'image/png'
    WHEN LOWER(SUBSTRING_INDEX(`file`, '.', -1)) = 'gif' THEN 'image/gif'
    WHEN LOWER(SUBSTRING_INDEX(`file`, '.', -1)) = 'pdf' THEN 'application/pdf'
    WHEN LOWER(SUBSTRING_INDEX(`file`, '.', -1)) = 'doc' THEN 'application/msword'
    WHEN LOWER(SUBSTRING_INDEX(`file`, '.', -1)) = 'docx' THEN 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    WHEN LOWER(SUBSTRING_INDEX(`file`, '.', -1)) = 'xls' THEN 'application/vnd.ms-excel'
    WHEN LOWER(SUBSTRING_INDEX(`file`, '.', -1)) = 'xlsx' THEN 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    WHEN LOWER(SUBSTRING_INDEX(`file`, '.', -1)) = 'zip' THEN 'application/zip'
    ELSE 'application/octet-stream'
END
WHERE `mime_type` IS NULL;