<?php
namespace App\Libraries;

use Illuminate\Database\Capsule\Manager as DB;
use App\Models\File;

class Storage
{
	
	public $path;
	public $id;
	public $error;
	public $file;

	public $status;
	public $message;
	public $target;
	public $resize_status;

	public $useFTP = true;

	// New properties for enhanced functionality
	protected $allowedMimeTypes = [];
	protected $maxFileSize;
	protected $uploadPath;
	protected $enableLogging = true;
	protected $generateThumbnails = false;
	protected $thumbnailSizes = [];

	public function __construct()
	{
		$this->uploadPath = conf("App.upload_path");
		$this->maxFileSize = conf('upload.max_size');
		$this->initializeDefaults();
	}

	/**
	 * Initialize default settings
	 */
	protected function initializeDefaults()
	{
		// Default allowed MIME types
		$this->allowedMimeTypes = [
			'image/jpeg' => ['jpg', 'jpeg'],
			'image/png' => ['png'],
			'image/gif' => ['gif'],
			'image/webp' => ['webp'],
			'application/pdf' => ['pdf'],
			'application/msword' => ['doc'],
			'application/vnd.openxmlformats-officedocument.wordprocessingml.document' => ['docx'],
			'application/vnd.ms-excel' => ['xls'],
			'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' => ['xlsx'],
			'text/csv' => ['csv'],
			'application/zip' => ['zip'],
			'application/x-rar-compressed' => ['rar'],
		];

		// Default thumbnail sizes
		$this->thumbnailSizes = [
			'thumb' => ['width' => 150, 'height' => 150],
			'medium' => ['width' => 500, 'height' => 500],
			'large' => ['width' => 1024, 'height' => 1024],
		];
	}

	/**
	 * Save uploaded file - backward compatible method
	 */
	public function save($file, $public = 0)
	{
		$save_to = $this->uploadPath;
		$save_to = rtrim($save_to, '/');

		$errors = [];
		$error = false;

		// Validate file upload
		if (!isset($file['error']) || is_array($file['error'])) {
			$error = true;
			$errors[] = ('Invalid parameters.');
		}

		$filepath = "";

		// Check upload errors
		switch ($file['error']) {
			case UPLOAD_ERR_OK:
				break;
			case UPLOAD_ERR_NO_FILE:
				$error = true;
				$errors[] = tr('No file sent.');
				break;
			case UPLOAD_ERR_INI_SIZE:
			case UPLOAD_ERR_FORM_SIZE:
				$error = true;
				$errors[] = tr('Exceeded filesize limit.');
				break;
			default:
				$error = true;
				$errors[] = tr('Unknown errors.');
		}

		// Enhanced file validation
		if (!$error) {
			$validationResult = $this->validateFile($file);
			if (!$validationResult['valid']) {
				$error = true;
				$errors = array_merge($errors, $validationResult['errors']);
			}
		}

		$extensions = conf('upload.ext');
		$temp = explode(".", $file["name"]);
		$old_file_name = $file["name"];
		$extension = strtolower(end($temp));
		
		$file_id = 0;
		$rand = rand(1, 1000000) . round(microtime(true));
		$file_name = $rand . '.' . $extension;
		
		// Create year/month subdirectories
		$subdir = date('Y/m');
		$full_path = $save_to . '/' . $subdir;
		
		if (!file_exists($full_path)) {
			mkdir($full_path, 0755, true);
		}
		
		$target = $full_path . '/' . basename($file_name);

		// File size validation
		if ($file['size'] > $this->maxFileSize) {
			$error = true;
			$errors[] = (tr('File size') . ' > ' . ($this->maxFileSize / 1000000) . "MB.");
		}

		// Move uploaded file
		if (!$error) {
			if (!move_uploaded_file($file['tmp_name'], $target)) {
				$error = true;
				$errors[] = tr('Failed to move uploaded file.');
			}
		}

		// Save to database
		if (!$error) {
			$fileModel = new File;
			$fileModel = $fileModel->create([
				"name" => $old_file_name,
				"file" => $subdir . '/' . $file_name,
				"size" => $file['size'],
				"mime_type" => $file['type'],
				"extension" => $extension,
				"public" => $public,
				"created_by" => auth() ? auth()->id : null,
			]);
			
			$file_id = $fileModel->id;

			// Generate thumbnails for images
			if ($this->generateThumbnails && $this->isImage($file['type'])) {
				$this->generateImageThumbnails($target, $full_path, $file_name);
			}

			// Log file upload
			if ($this->enableLogging) {
				$this->logFileOperation('upload', $file_id, $old_file_name);
			}
		}
		
		// Set backward compatible properties
		$this->status = !$error;
		$this->message = $errors;
		$this->file = $file_name;
		$this->id = $file_id;
		$this->target = $target;

		return $this;
	}

	/**
	 * Enhanced file validation
	 */
	protected function validateFile($file)
	{
		$errors = [];
		$valid = true;

		// Validate MIME type
		$finfo = finfo_open(FILEINFO_MIME_TYPE);
		$mimeType = finfo_file($finfo, $file['tmp_name']);
		finfo_close($finfo);

		if (!array_key_exists($mimeType, $this->allowedMimeTypes)) {
			$valid = false;
			$errors[] = tr('File type not allowed: ') . $mimeType;
		}

		// Validate extension matches MIME type
		$temp = explode(".", $file["name"]);
		$extension = strtolower(end($temp));
		
		if (isset($this->allowedMimeTypes[$mimeType]) && 
			!in_array($extension, $this->allowedMimeTypes[$mimeType])) {
			$valid = false;
			$errors[] = tr('File extension does not match file type');
		}

		// Additional security checks for images
		if (strpos($mimeType, 'image/') === 0) {
			$imageInfo = getimagesize($file['tmp_name']);
			if ($imageInfo === false) {
				$valid = false;
				$errors[] = tr('Invalid image file');
			}
		}

		return ['valid' => $valid, 'errors' => $errors];
	}

	/**
	 * Check if file is an image
	 */
	protected function isImage($mimeType)
	{
		return strpos($mimeType, 'image/') === 0;
	}

	/**
	 * Generate thumbnails for uploaded images
	 */
	protected function generateImageThumbnails($sourcePath, $targetDir, $fileName)
	{
		$imageInfo = getimagesize($sourcePath);
		if ($imageInfo === false) {
			return false;
		}

		$sourceImage = $this->createImageFromFile($sourcePath, $imageInfo['mime']);
		if (!$sourceImage) {
			return false;
		}

		$sourceWidth = imagesx($sourceImage);
		$sourceHeight = imagesy($sourceImage);

		foreach ($this->thumbnailSizes as $sizeName => $dimensions) {
			$thumbDir = $targetDir . '/' . $sizeName;
			if (!file_exists($thumbDir)) {
				mkdir($thumbDir, 0755, true);
			}

			// Calculate new dimensions maintaining aspect ratio
			$ratio = min($dimensions['width'] / $sourceWidth, $dimensions['height'] / $sourceHeight);
			$newWidth = round($sourceWidth * $ratio);
			$newHeight = round($sourceHeight * $ratio);

			// Create thumbnail
			$thumbnail = imagecreatetruecolor($newWidth, $newHeight);
			
			// Preserve transparency for PNG and WebP
			if ($imageInfo['mime'] == 'image/png' || $imageInfo['mime'] == 'image/webp') {
				imagealphablending($thumbnail, false);
				imagesavealpha($thumbnail, true);
				$transparent = imagecolorallocatealpha($thumbnail, 255, 255, 255, 127);
				imagefilledrectangle($thumbnail, 0, 0, $newWidth, $newHeight, $transparent);
			}

			imagecopyresampled(
				$thumbnail, $sourceImage,
				0, 0, 0, 0,
				$newWidth, $newHeight,
				$sourceWidth, $sourceHeight
			);

			// Save thumbnail
			$thumbPath = $thumbDir . '/' . $fileName;
			$this->saveImageToFile($thumbnail, $thumbPath, $imageInfo['mime']);
			imagedestroy($thumbnail);
		}

		imagedestroy($sourceImage);
		return true;
	}

	/**
	 * Create image resource from file
	 */
	protected function createImageFromFile($path, $mimeType)
	{
		switch ($mimeType) {
			case 'image/jpeg':
				return imagecreatefromjpeg($path);
			case 'image/png':
				return imagecreatefrompng($path);
			case 'image/gif':
				return imagecreatefromgif($path);
			case 'image/webp':
				return imagecreatefromwebp($path);
			default:
				return false;
		}
	}

	/**
	 * Save image resource to file
	 */
	protected function saveImageToFile($image, $path, $mimeType, $quality = 85)
	{
		switch ($mimeType) {
			case 'image/jpeg':
				return imagejpeg($image, $path, $quality);
			case 'image/png':
				return imagepng($image, $path, 9);
			case 'image/gif':
				return imagegif($image, $path);
			case 'image/webp':
				return imagewebp($image, $path, $quality);
			default:
				return false;
		}
	}

	/**
	 * Upload multiple files
	 */
	public function saveMultiple($files, $public = 0)
	{
		$results = [];
		
		// Reorganize files array
		$fileCount = count($files['name']);
		for ($i = 0; $i < $fileCount; $i++) {
			$file = [
				'name' => $files['name'][$i],
				'type' => $files['type'][$i],
				'tmp_name' => $files['tmp_name'][$i],
				'error' => $files['error'][$i],
				'size' => $files['size'][$i]
			];
			
			$result = $this->save($file, $public);
			$results[] = [
				'id' => $result->id,
				'file' => $result->file,
				'status' => $result->status,
				'errors' => $result->message
			];
		}
		
		return $results;
	}

	/**
	 * Delete file with cleanup
	 */
	public function delete($fileId)
	{
		$fileModel = File::find($fileId);
		if (!$fileModel) {
			return false;
		}

		$filePath = $this->uploadPath . '/' . $fileModel->file;
		
		// Delete main file
		if (file_exists($filePath)) {
			unlink($filePath);
		}

		// Delete thumbnails if they exist
		$pathInfo = pathinfo($filePath);
		$dir = $pathInfo['dirname'];
		$fileName = $pathInfo['basename'];
		
		foreach ($this->thumbnailSizes as $sizeName => $dimensions) {
			$thumbPath = $dir . '/' . $sizeName . '/' . $fileName;
			if (file_exists($thumbPath)) {
				unlink($thumbPath);
			}
		}

		// Log deletion
		if ($this->enableLogging) {
			$this->logFileOperation('delete', $fileId, $fileModel->name);
		}

		// Delete from database
		$fileModel->delete();
		
		return true;
	}

	/**
	 * Log file operations
	 */
	protected function logFileOperation($operation, $fileId, $fileName)
	{
		// This can be extended to log to database or file
		// For now, using error_log as a simple implementation
		error_log(sprintf(
			"[Storage] %s: File ID %d, Name: %s, User: %s",
			$operation,
			$fileId,
			$fileName,
			auth() ? auth()->id : 'anonymous'
		));
	}

	/**
	 * Get file with thumbnail support
	 */
	public function get($file, $type = "")
	{
		$uploads_path = $this->uploadPath;
		$uploads_path = rtrim($uploads_path, '/');

		if (is_numeric($file)) {
			$file_data = File::find($file);
		} else {
			$file_data = File::where("file", $file)->first();
		}

		if ($file_data) {
			// If type is specified (thumbnail size), return thumbnail URL
			if ($type && in_array($type, array_keys($this->thumbnailSizes))) {
				$pathInfo = pathinfo($file_data['file']);
				$thumbFile = $pathInfo['dirname'] . '/' . $type . '/' . $pathInfo['basename'];
				
				// Check if thumbnail exists
				if (file_exists($uploads_path . '/' . $thumbFile)) {
					return base_url("uploads/" . urlencode($thumbFile));
				}
			}
			
			// Return original file URL
			return base_url("uploads/" . urlencode($file_data['file']));
		}

		return false;
	}

	/**
	 * Get file information
	 */
	public function getFileInfo($fileId)
	{
		$file = File::find($fileId);
		if (!$file) {
			return false;
		}

		$filePath = $this->uploadPath . '/' . $file->file;
		$info = [
			'id' => $file->id,
			'name' => $file->name,
			'file' => $file->file,
			'size' => $file->size,
			'mime_type' => $file->mime_type,
			'extension' => $file->extension,
			'url' => $this->get($file->id),
			'created_at' => $file->created_at,
			'exists' => file_exists($filePath)
		];

		// Add image-specific information
		if ($this->isImage($file->mime_type) && file_exists($filePath)) {
			$imageInfo = getimagesize($filePath);
			$info['width'] = $imageInfo[0];
			$info['height'] = $imageInfo[1];
			
			// Add thumbnail URLs
			$info['thumbnails'] = [];
			foreach ($this->thumbnailSizes as $sizeName => $dimensions) {
				$thumbUrl = $this->get($file->id, $sizeName);
				if ($thumbUrl) {
					$info['thumbnails'][$sizeName] = $thumbUrl;
				}
			}
		}

		return $info;
	}

	/**
	 * Set allowed MIME types
	 */
	public function setAllowedTypes($mimeTypes)
	{
		$this->allowedMimeTypes = $mimeTypes;
		return $this;
	}

	/**
	 * Enable/disable thumbnail generation
	 */
	public function enableThumbnails($enable = true, $sizes = null)
	{
		$this->generateThumbnails = $enable;
		if ($sizes !== null) {
			$this->thumbnailSizes = $sizes;
		}
		return $this;
	}

	/**
	 * Set maximum file size
	 */
	public function setMaxSize($bytes)
	{
		$this->maxFileSize = $bytes;
		return $this;
	}

	// Backward compatible methods
	public function saved()
	{
		return (bool)$this->status;
	}

	public function errors()
	{
		return $this->message;
	}

	public function remove()
	{
		// Backward compatibility - redirect to delete
		if ($this->id) {
			return $this->delete($this->id);
		}
		return false;
	}

	public function input($conf = [])
	{
		$name = "file";
		$config = $conf;
		$id = "file-" . rand(10000000, 999999999);

		if (isset($config["id"])) {
			$id = $config["id"];
			unset($config["id"]);
		}

		if (isset($config["name"])) {
			$name = $config["name"];
			unset($config["name"]);
		}

		// Support for multiple files
		$multiple = '';
		if (isset($config["multiple"]) && $config["multiple"]) {
			$multiple = 'multiple';
			$name .= '[]';
			unset($config["multiple"]);
		}

		$onchange = "$('#has_" . $id . "').val('true');";
		if (isset($config["onchange"])) {
			$onchange .= $config["onchange"];
			unset($config["onchange"]);
		}

		// Add accept attribute based on allowed types
		if (!isset($config["accept"]) && !empty($this->allowedMimeTypes)) {
			$extensions = [];
			foreach ($this->allowedMimeTypes as $mime => $exts) {
				foreach ($exts as $ext) {
					$extensions[] = '.' . $ext;
				}
			}
			$config["accept"] = implode(',', $extensions);
		}

		$attrs = "";
		$array_keys = array_keys($config);
		foreach ($array_keys as $key) {
			$attrs .= $key . '="' . $config[$key] . '" ';
		}

		$html = '';
		$html .= '<input type="file" name="' . $name . '" id="' . $id . '" ' . $attrs . ' ' . $multiple . ' onchange="' . $onchange . '">';
		$html .= '<input type="text" name="has_' . $name . '" id="has_' . $id . '" value="false" hidden>';

		return $html;
	}

	public function url($file)
	{
		return $this->get($file);
	}

	public function read($remoteFilePath)
	{
		$save_to = env("storage.path");
		$save_to = rtrim($save_to, '/');
		$errors = [];
		$error = false;

		$ftp_conn = $this->initializeFTP();
		ftp_pasv($ftp_conn, true);

		// The path where the file is stored on the FTP server
		$remoteFilePath = $save_to . '/' . $remoteFilePath;

		// Temporary file to store the downloaded data
		$tempHandle = fopen('php://temp', 'r+');

		if (!ftp_fget($ftp_conn, $tempHandle, $remoteFilePath, FTP_BINARY, 0)) {
			fclose($tempHandle);
			ftp_close($ftp_conn);
			return false;
		}

		// If successful, read the file content from the temp stream
		rewind($tempHandle);
		$fileContent = stream_get_contents($tempHandle);
		fclose($tempHandle);
		ftp_close($ftp_conn);

		return $fileContent;
	}

	private function initializeFTP()
	{
		$ftp_server = env('storage.ftp_server');
		$ftp_username = env('storage.ftp_username');
		$ftp_password = env('storage.ftp_password');

		// Establish the FTP connection.
		$ftp_conn = ftp_connect($ftp_server);

		// Login to the FTP server.
		$login = ftp_login($ftp_conn, $ftp_username, $ftp_password);

		if (!$ftp_conn || !$login) {
			die('FTP connection failed.');
		}

		return $ftp_conn;
	}
}