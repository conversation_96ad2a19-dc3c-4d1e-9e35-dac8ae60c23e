<?php 

use App\Models\Sale;
use App\Models\ProductLine;
use App\Models\Payment;

require(__DIR__."/../../php/init.php");

$sale_id = input("i");
$sale = Sale::find($sale_id);

if (!$sale) {
    _error_code(404);
}

$items = ProductLine::where("reltype", 'sales')->where("relid", $sale->id)->get();
$payments = Payment::where("reltype", 'sales')->where("relid", $sale->id)->get();

$page = [
    "title" => tr("Invoice") . " #" . $sale->id,
];

?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= tr("Invoice") ?> #<?= $sale->id ?></title>
    <link href="<?= base_url() ?>assets2/css/ltr/bootstrap.min.css" rel="stylesheet" type="text/css">
    <link rel="stylesheet" href="<?= base_url('assets/fontawesome/css/all.min.css') ?>">
    <style>
        @page {
            size: A4;
            margin: 0;
        }
        
        body {
            font-family: Arial, sans-serif;
            background-color: #f8f9fa;
            margin: 0;
            padding: 0;
        }
        
        .invoice-container {
            width: 210mm;
            min-height: 297mm;
            margin: 0 auto;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        
        .invoice-content {
            padding: 30mm 20mm;
        }
        
        .invoice-header {
            display: flex;
            justify-content: space-between;
            align-items: start;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #dee2e6;
        }
        
        .company-section {
            flex: 1;
        }
        
        .invoice-section {
            text-align: right;
            flex: 1;
        }
        
        .company-logo {
            max-height: 80px;
            margin-bottom: 15px;
        }
        
        .company-name {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        
        .company-details {
            font-size: 14px;
            color: #666;
            line-height: 1.6;
        }
        
        .invoice-title {
            font-size: 36px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        
        .invoice-number {
            font-size: 18px;
            color: #666;
            margin-bottom: 5px;
        }
        
        .invoice-date {
            font-size: 14px;
            color: #666;
        }
        
        .parties-section {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
            gap: 30px;
        }
        
        .party-box {
            flex: 1;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        .party-title {
            font-size: 14px;
            font-weight: bold;
            color: #666;
            margin-bottom: 10px;
            text-transform: uppercase;
        }
        
        .party-details {
            font-size: 14px;
            color: #333;
            line-height: 1.6;
        }
        
        .invoice-table {
            width: 100%;
            margin-bottom: 30px;
        }
        
        .invoice-table th {
            background: #343a40;
            color: white;
            padding: 12px;
            font-size: 14px;
            text-align: left;
            border: none;
        }
        
        .invoice-table td {
            padding: 12px;
            font-size: 14px;
            border-bottom: 1px solid #dee2e6;
        }
        
        .invoice-table .text-right {
            text-align: right;
        }
        
        .invoice-table .text-center {
            text-align: center;
        }
        
        .invoice-summary {
            display: flex;
            justify-content: flex-end;
            margin-bottom: 30px;
        }
        
        .summary-box {
            width: 300px;
        }
        
        .summary-row {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            font-size: 14px;
        }
        
        .summary-row.total {
            font-size: 18px;
            font-weight: bold;
            border-top: 2px solid #333;
            padding-top: 15px;
            margin-top: 10px;
        }
        
        .payment-section {
            margin-bottom: 30px;
        }
        
        .payment-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }
        
        .payment-table {
            width: 100%;
            margin-bottom: 20px;
        }
        
        .payment-table th {
            background: #f8f9fa;
            padding: 10px;
            font-size: 14px;
            text-align: left;
            border-bottom: 2px solid #dee2e6;
        }
        
        .payment-table td {
            padding: 10px;
            font-size: 14px;
            border-bottom: 1px solid #dee2e6;
        }
        
        .notes-section {
            margin-bottom: 30px;
        }
        
        .note-box {
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            margin-bottom: 15px;
        }
        
        .note-title {
            font-size: 14px;
            font-weight: bold;
            color: #666;
            margin-bottom: 10px;
        }
        
        .note-content {
            font-size: 14px;
            color: #333;
            line-height: 1.6;
        }
        
        .invoice-footer {
            text-align: center;
            padding-top: 20px;
            border-top: 1px solid #dee2e6;
            color: #666;
            font-size: 12px;
        }
        
        .action-buttons {
            padding: 20px;
            background: #f8f9fa;
            text-align: center;
            border-top: 1px solid #dee2e6;
        }
        
        @media print {
            body {
                background: white;
                margin: 0;
            }
            
            .invoice-container {
                box-shadow: none;
                margin: 0;
            }
            
            .action-buttons {
                display: none;
            }
            
            .invoice-content {
                padding: 20mm;
            }
        }
    </style>
</head>
<body>
    <div class="invoice-container">
        <div class="invoice-content">
            <!-- Header -->
            <div class="invoice-header">
                <div class="company-section">
                <?= _branch()->option('receipt_header') ?>
                    <div class="company-name"><?= get_option('business_name', 'StockV3') ?></div>
                    <div class="company-details">
                        <?php if (get_option('company_address')): ?>
                            <?= nl2br(get_option('company_address')) ?><br>
                        <?php endif; ?>
                        <?php if (get_option('company_phone')): ?>
                            <?= tr("Phone") ?>: <?= get_option('company_phone') ?><br>
                        <?php endif; ?>
                        <?php if (get_option('company_email')): ?>
                            <?= tr("Email") ?>: <?= get_option('company_email') ?><br>
                        <?php endif; ?>
                        <?php if (get_option('company_vat')): ?>
                            <?= tr("VAT") ?>: <?= get_option('company_vat') ?>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="invoice-section">
                    <div class="invoice-title"><?= tr("INVOICE") ?></div>
                    <div class="invoice-number"><?= get_option('invoice_prefix', 'INV-') ?><?= str_pad($sale->id, 6, '0', STR_PAD_LEFT) ?></div>
                    <div class="invoice-date"><?= tr("Date") ?>: <?= date('d/m/Y', strtotime($sale->date)) ?></div>
                    <?php if($sale->due_date): ?>
                        <div class="invoice-date"><?= tr("Due Date") ?>: <?= date('d/m/Y', strtotime($sale->due_date)) ?></div>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Bill To / Ship To -->
            <div class="parties-section">
                <div class="party-box">
                    <div class="party-title"><?= tr("Bill To") ?></div>
                    <div class="party-details">
                        <strong><?= $sale->customer ? $sale->customer->name : tr("Walk-in Customer") ?></strong><br>
                        <?php if($sale->customer): ?>
                            <?php if($sale->customer->address): ?>
                                <?= nl2br($sale->customer->address) ?><br>
                            <?php endif; ?>
                            <?php if($sale->customer->phone): ?>
                                <?= tr("Phone") ?>: <?= $sale->customer->phone ?><br>
                            <?php endif; ?>
                            <?php if($sale->customer->email): ?>
                                <?= tr("Email") ?>: <?= $sale->customer->email ?><br>
                            <?php endif; ?>
                            <?php if($sale->customer->vat_number): ?>
                                <?= tr("VAT") ?>: <?= $sale->customer->vat_number ?>
                            <?php endif; ?>
                        <?php endif; ?>
                    </div>
                </div>
                
                <?php if(get_option('invoice_show_shipping', '1') == '1'): ?>
                <div class="party-box">
                    <div class="party-title"><?= tr("Ship To") ?></div>
                    <div class="party-details">
                        <?php if($sale->shipping_address): ?>
                            <?= nl2br($sale->shipping_address) ?>
                        <?php else: ?>
                            <em><?= tr("Same as billing address") ?></em>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endif; ?>
            </div>
            
            <!-- Invoice Items -->
            <table class="invoice-table">
                <thead>
                    <tr>
                        <th width="5%">#</th>
                        <th width="35%"><?= tr("Item") ?></th>
                        <th width="10%" class="text-center"><?= tr("Qty") ?></th>
                        <th width="15%" class="text-right"><?= tr("Unit Price") ?></th>
                        <th width="10%" class="text-center"><?= tr("Tax") ?></th>
                        <th width="10%" class="text-center"><?= tr("Discount") ?></th>
                        <th width="15%" class="text-right"><?= tr("Total") ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($items as $key => $item): ?>
                        <tr>
                            <td><?= $key + 1 ?></td>
                            <td>
                                <strong><?= $item->name ?></strong>
                                <?php if ($item->description): ?>
                                    <br><small class="text-muted"><?= $item->description ?></small>
                                <?php endif; ?>
                            </td>
                            <td class="text-center"><?= _float($item->quantity) ?></td>
                            <td class="text-right"><?= _format_price($item->price, false) ?></td>
                            <td class="text-center">
                                <?php if ($item->tax_percent > 0): ?>
                                    <?= $item->tax_percent ?>%
                                <?php else: ?>
                                    -
                                <?php endif; ?>
                            </td>
                            <td class="text-center">
                                <?php if ($item->discount > 0): ?>
                                    <?= _format_price($item->discount, false) ?>
                                <?php else: ?>
                                    -
                                <?php endif; ?>
                            </td>
                            <td class="text-right"><?= _format_price($item->total_price * $item->quantity) ?></td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
            
            <!-- Invoice Summary -->
            <div class="invoice-summary">
                <div class="summary-box">
                    <div class="summary-row">
                        <span><?= tr("Subtotal") ?>:</span>
                        <span><?= _format_price($sale->total_price - $sale->total_tax + $sale->discount) ?></span>
                    </div>
                    
                    <?php if ($sale->total_tax > 0): ?>
                        <div class="summary-row">
                            <span><?= tr("Tax") ?>:</span>
                            <span><?= _format_price($sale->total_tax) ?></span>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($sale->discount > 0): ?>
                        <div class="summary-row">
                            <span><?= tr("Discount") ?>:</span>
                            <span>-<?= _format_price($sale->discount) ?></span>
                        </div>
                    <?php endif; ?>
                    
                    <div class="summary-row total">
                        <span><?= tr("Total") ?>:</span>
                        <span><?= _format_price($sale->total_price) ?></span>
                    </div>
                </div>
            </div>
            
            <!-- Payments -->
            <?php if ($payments->count() > 0 && get_option('invoice_show_payment_info', '1') == '1'): ?>
                <div class="payment-section">
                    <div class="payment-title"><?= tr("Payment Information") ?></div>
                    <table class="payment-table">
                        <thead>
                            <tr>
                                <th><?= tr("Date") ?></th>
                                <th><?= tr("Method") ?></th>
                                <th><?= tr("Reference") ?></th>
                                <th class="text-right"><?= tr("Amount") ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($payments as $payment): ?>
                                <tr>
                                    <td><?= date('d/m/Y', strtotime($payment->created_at)) ?></td>
                                    <td><?= Payment::getPaymentMethods()[$payment->method] ?? $payment->method ?></td>
                                    <td><?= $payment->reference ?: '-' ?></td>
                                    <td class="text-right"><?= _format_price($payment->amount) ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                        <tfoot>
                            <tr>
                                <th colspan="3" class="text-right"><?= tr("Total Paid") ?>:</th>
                                <th class="text-right"><?= _format_price($sale->paid) ?></th>
                            </tr>
                            <?php if($sale->total_price > $sale->paid): ?>
                                <tr>
                                    <th colspan="3" class="text-right text-danger"><?= tr("Balance Due") ?>:</th>
                                    <th class="text-right text-danger"><?= _format_price($sale->total_price - $sale->paid) ?></th>
                                </tr>
                            <?php endif; ?>
                        </tfoot>
                    </table>
                </div>
            <?php endif; ?>
            
            <!-- Notes -->
            <?php if($sale->customer_note || $sale->admin_note): ?>
                <div class="notes-section">
                    <?php if($sale->customer_note): ?>
                        <div class="note-box">
                            <div class="note-title"><?= tr("Customer Note") ?></div>
                            <div class="note-content"><?= nl2br($sale->customer_note) ?></div>
                        </div>
                    <?php endif; ?>
                    
                    <?php if($sale->admin_note): ?>
                        <div class="note-box">
                            <div class="note-title"><?= tr("Terms & Conditions") ?></div>
                            <div class="note-content"><?= nl2br($sale->admin_note) ?></div>
                        </div>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
            
            <!-- Terms & Conditions -->
            <?php if(get_option('invoice_terms') && get_option('invoice_show_notes', '1') == '1'): ?>
                <div class="notes-section">
                    <div class="note-box">
                        <div class="note-title"><?= tr("Terms & Conditions") ?></div>
                        <div class="note-content"><?= nl2br(get_option('invoice_terms')) ?></div>
                    </div>
                </div>
            <?php endif; ?>
            
            <!-- Signature Area -->
            <?php if(get_option('invoice_show_signature', '0') == '1'): ?>
                <div style="margin-top: 60px; display: flex; justify-content: flex-end;">
                    <div style="width: 300px; text-align: center;">
                        <div style="border-bottom: 1px solid #333; margin-bottom: 10px; height: 40px;"></div>
                        <div style="font-size: 14px; color: #666;"><?= tr("Authorized Signature") ?></div>
                    </div>
                </div>
            <?php endif; ?>
            
            <!-- Footer -->
            <div class="invoice-footer">
                <p><?= tr("Thank you for your business!") ?></p>
                <?php if (get_option('invoice_footer')): ?>
                    <p><?= nl2br(get_option('invoice_footer')) ?></p>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- Action Buttons -->
        <div class="action-buttons">
            <button type="button" class="btn btn-success btn-lg" onclick="window.print()">
                <i class="fa fa-print mr-2"></i><?= tr("Print Invoice") ?>
            </button>
            <button type="button" class="btn btn-secondary btn-lg" onclick="goBack()">
                <i class="fa fa-arrow-left mr-2"></i><?= tr("Back") ?>
            </button>
        </div>
    </div>

    <script>
        function goBack() {
            if (window.history.length > 1) {
                window.history.back();
            } else {
                window.close();
                setTimeout(function() {
                    window.location.href = '<?= base_url("sales/view.php?i=" . $sale->id) ?>';
                }, 100);
            }
        }
        
        document.addEventListener('DOMContentLoaded', function() {
            document.querySelector('.btn-success').focus();
        });
        
        document.addEventListener('keydown', function(e) {
            if ((e.ctrlKey || e.metaKey) && e.key === 'p') {
                e.preventDefault();
                window.print();
            }
            if (e.key === 'Escape') {
                goBack();
            }
        });
    </script>
</body>
</html>