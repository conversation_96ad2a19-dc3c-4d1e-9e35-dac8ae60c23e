<?php 

use App\Models\Sale;
use App\Models\ProductLine;
use App\Models\Payment;

require(__DIR__."/../../php/init.php");

$sale_id = input("i");
$sale = Sale::find($sale_id);

if (!$sale) {
    _error_code(404);
}

$items = ProductLine::where("reltype", 'sales')->where("relid", $sale->id)->get();

$page = [
    "title" => tr("Delivery Note") . " #" . $sale->id,
];

?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= tr("Delivery Note") ?> #<?= $sale->id ?></title>
    <link href="<?= base_url() ?>assets2/css/ltr/bootstrap.min.css" rel="stylesheet" type="text/css">
    <link rel="stylesheet" href="<?= base_url('assets/fontawesome/css/all.min.css') ?>">
    <style>
        @page {
            size: A4;
            margin: 0;
        }
        
        body {
            font-family: Arial, sans-serif;
            background-color: #f8f9fa;
            margin: 0;
            padding: 0;
        }
        
        .delivery-container {
            width: 210mm;
            min-height: 297mm;
            margin: 0 auto;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        
        .delivery-content {
            padding: 30mm 20mm;
        }
        
        .delivery-header {
            display: flex;
            justify-content: space-between;
            align-items: start;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #dee2e6;
        }
        
        .company-section {
            flex: 1;
        }
        
        .delivery-section {
            text-align: right;
            flex: 1;
        }
        
        .company-logo {
            max-height: 80px;
            margin-bottom: 15px;
        }
        
        .company-name {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        
        .company-details {
            font-size: 14px;
            color: #666;
            line-height: 1.6;
        }
        
        .delivery-title {
            font-size: 36px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        
        .delivery-number {
            font-size: 18px;
            color: #666;
            margin-bottom: 5px;
        }
        
        .delivery-date {
            font-size: 14px;
            color: #666;
        }
        
        .parties-section {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
            gap: 30px;
        }
        
        .party-box {
            flex: 1;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        .party-title {
            font-size: 14px;
            font-weight: bold;
            color: #666;
            margin-bottom: 10px;
            text-transform: uppercase;
        }
        
        .party-details {
            font-size: 14px;
            color: #333;
            line-height: 1.6;
        }
        
        .delivery-info {
            margin-bottom: 30px;
            padding: 20px;
            background: #e8f4f8;
            border-radius: 8px;
            border-left: 4px solid #17a2b8;
        }
        
        .delivery-info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
        }
        
        .delivery-info-label {
            font-weight: bold;
            color: #333;
        }
        
        .delivery-table {
            width: 100%;
            margin-bottom: 30px;
        }
        
        .delivery-table th {
            background: #17a2b8;
            color: white;
            padding: 12px;
            font-size: 14px;
            text-align: left;
            border: none;
        }
        
        .delivery-table td {
            padding: 12px;
            font-size: 14px;
            border-bottom: 1px solid #dee2e6;
        }
        
        .delivery-table .text-right {
            text-align: right;
        }
        
        .delivery-table .text-center {
            text-align: center;
        }
        
        .signature-section {
            margin-top: 60px;
            display: flex;
            justify-content: space-between;
            gap: 30px;
        }
        
        .signature-box {
            flex: 1;
            text-align: center;
        }
        
        .signature-line {
            border-bottom: 1px solid #333;
            margin-bottom: 10px;
            height: 40px;
        }
        
        .signature-label {
            font-size: 14px;
            color: #666;
        }
        
        .notes-section {
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        .note-title {
            font-size: 16px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        
        .note-content {
            font-size: 14px;
            color: #333;
            line-height: 1.6;
        }
        
        .delivery-footer {
            text-align: center;
            padding-top: 20px;
            border-top: 1px solid #dee2e6;
            color: #666;
            font-size: 12px;
        }
        
        .action-buttons {
            padding: 20px;
            background: #f8f9fa;
            text-align: center;
            border-top: 1px solid #dee2e6;
        }
        
        .stamp-box {
            border: 2px dashed #ccc;
            padding: 20px;
            text-align: center;
            color: #999;
            margin-top: 20px;
            min-height: 100px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        @media print {
            body {
                background: white;
                margin: 0;
            }
            
            .delivery-container {
                box-shadow: none;
                margin: 0;
            }
            
            .action-buttons {
                display: none;
            }
            
            .delivery-content {
                padding: 20mm;
            }
        }
    </style>
</head>
<body>
    <div class="delivery-container">
        <div class="delivery-content">
            <!-- Header -->
            <div class="delivery-header">
                <div class="company-section">
                <?= _branch()->option('receipt_header') ?>
                    <div class="company-name"><?= get_option('business_name', 'StockV3') ?></div>
                    <div class="company-details">
                        <?php if (get_option('company_address')): ?>
                            <?= nl2br(get_option('company_address')) ?><br>
                        <?php endif; ?>
                        <?php if (get_option('company_phone')): ?>
                            <?= tr("Phone") ?>: <?= get_option('company_phone') ?><br>
                        <?php endif; ?>
                        <?php if (get_option('company_email')): ?>
                            <?= tr("Email") ?>: <?= get_option('company_email') ?>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="delivery-section">
                    <div class="delivery-title"><?= tr("DELIVERY NOTE") ?></div>
                    <div class="delivery-number">#<?= str_pad($sale->id, 6, '0', STR_PAD_LEFT) ?></div>
                    <div class="delivery-date"><?= tr("Date") ?>: <?= date('d/m/Y', strtotime($sale->created_at)) ?></div>
                </div>
            </div>
            
            <!-- Delivery From / To -->
            <div class="parties-section">
                <div class="party-box">
                    <div class="party-title"><?= tr("From") ?></div>
                    <div class="party-details">
                        <strong><?= $sale->branch->name ?></strong><br>
                        <?php if($sale->branch->address): ?>
                            <?= nl2br($sale->branch->address) ?><br>
                        <?php endif; ?>
                        <?php if($sale->branch->phone): ?>
                            <?= tr("Phone") ?>: <?= $sale->branch->phone ?><br>
                        <?php endif; ?>
                        <?php if($sale->branch->email): ?>
                            <?= tr("Email") ?>: <?= $sale->branch->email ?>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="party-box">
                    <div class="party-title"><?= tr("Deliver To") ?></div>
                    <div class="party-details">
                        <strong><?= $sale->customer ? $sale->customer->name : tr("Walk-in Customer") ?></strong><br>
                        <?php if($sale->shipping_address): ?>
                            <?= nl2br($sale->shipping_address) ?>
                        <?php elseif($sale->customer && $sale->customer->address): ?>
                            <?= nl2br($sale->customer->address) ?><br>
                        <?php endif; ?>
                        <?php if($sale->customer && $sale->customer->phone): ?>
                            <?= tr("Phone") ?>: <?= $sale->customer->phone ?><br>
                        <?php endif; ?>
                        <?php if($sale->customer && $sale->customer->email): ?>
                            <?= tr("Email") ?>: <?= $sale->customer->email ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <!-- Delivery Information -->
            <div class="delivery-info">
                <div class="delivery-info-row">
                    <span class="delivery-info-label"><?= tr("Invoice Reference") ?>:</span>
                    <span>#<?= str_pad($sale->id, 6, '0', STR_PAD_LEFT) ?></span>
                </div>
                <div class="delivery-info-row">
                    <span class="delivery-info-label"><?= tr("Delivery Date") ?>:</span>
                    <span><?= date('d/m/Y') ?></span>
                </div>
                <div class="delivery-info-row">
                    <span class="delivery-info-label"><?= tr("Delivery Method") ?>:</span>
                    <span><?= tr("Standard Delivery") ?></span>
                </div>
            </div>
            
            <!-- Delivery Items -->
            <table class="delivery-table">
                <thead>
                    <tr>
                        <th width="5%">#</th>
                        <th width="15%"><?= tr("SKU") ?></th>
                        <th width="40%"><?= tr("Description") ?></th>
                        <th width="15%" class="text-center"><?= tr("Ordered Qty") ?></th>
                        <th width="15%" class="text-center"><?= tr("Delivered Qty") ?></th>
                        <th width="10%" class="text-center"><?= tr("Unit") ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($items as $key => $item): ?>
                        <?php 
                        $product = $item->product;
                        ?>
                        <tr>
                            <td><?= $key + 1 ?></td>
                            <td><?= $product ? $product->sku : '-' ?></td>
                            <td>
                                <strong><?= $item->name ?></strong>
                                <?php if ($item->description): ?>
                                    <br><small class="text-muted"><?= $item->description ?></small>
                                <?php endif; ?>
                            </td>
                            <td class="text-center"><?= _float($item->quantity) ?></td>
                            <td class="text-center">
                                <strong><?= _float($item->quantity) ?></strong>
                            </td>
                            <td class="text-center"><?= $product && $product->unit ? $product->unit : tr("PCS") ?></td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
                <tfoot>
                    <tr>
                        <th colspan="3" class="text-right"><?= tr("Total Items") ?>:</th>
                        <th class="text-center"><?= _float($items->sum('quantity')) ?></th>
                        <th class="text-center"><strong><?= _float($items->sum('quantity')) ?></strong></th>
                        <th></th>
                    </tr>
                </tfoot>
            </table>
            
            <!-- Notes -->
            <?php if($sale->customer_note): ?>
                <div class="notes-section">
                    <div class="note-title"><?= tr("Delivery Instructions") ?></div>
                    <div class="note-content"><?= nl2br($sale->customer_note) ?></div>
                </div>
            <?php endif; ?>
            
            <!-- Signature Section -->
            <div class="signature-section">
                <div class="signature-box">
                    <div class="signature-line"></div>
                    <div class="signature-label"><?= tr("Prepared By") ?></div>
                    <div class="mt-2">
                        <small><?= tr("Name") ?>: ________________________</small><br>
                        <small><?= tr("Date") ?>: ________________________</small>
                    </div>
                </div>
                
                <div class="signature-box">
                    <div class="signature-line"></div>
                    <div class="signature-label"><?= tr("Delivered By") ?></div>
                    <div class="mt-2">
                        <small><?= tr("Name") ?>: ________________________</small><br>
                        <small><?= tr("Date") ?>: ________________________</small>
                    </div>
                </div>
                
                <div class="signature-box">
                    <div class="signature-line"></div>
                    <div class="signature-label"><?= tr("Received By") ?></div>
                    <div class="mt-2">
                        <small><?= tr("Name") ?>: ________________________</small><br>
                        <small><?= tr("Date") ?>: ________________________</small>
                    </div>
                </div>
            </div>
            
            <!-- Company Stamp -->
            <div class="stamp-box">
                <span><?= tr("Company Stamp") ?></span>
            </div>
            
            <!-- Footer -->
            <div class="delivery-footer">
                <p><?= tr("This is a computer generated document and does not require signature") ?></p>
                <?php if (get_option('delivery_footer')): ?>
                    <p><?= get_option('delivery_footer') ?></p>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- Action Buttons -->
        <div class="action-buttons">
            <button type="button" class="btn btn-info btn-lg" onclick="window.print()">
                <i class="fa fa-print mr-2"></i><?= tr("Print Delivery Note") ?>
            </button>
            <button type="button" class="btn btn-secondary btn-lg" onclick="goBack()">
                <i class="fa fa-arrow-left mr-2"></i><?= tr("Back") ?>
            </button>
        </div>
    </div>

    <script>
        function goBack() {
            if (window.history.length > 1) {
                window.history.back();
            } else {
                window.close();
                setTimeout(function() {
                    window.location.href = '<?= base_url("sales/view.php?i=" . $sale->id) ?>';
                }, 100);
            }
        }
        
        document.addEventListener('DOMContentLoaded', function() {
            document.querySelector('.btn-info').focus();
        });
        
        document.addEventListener('keydown', function(e) {
            if ((e.ctrlKey || e.metaKey) && e.key === 'p') {
                e.preventDefault();
                window.print();
            }
            if (e.key === 'Escape') {
                goBack();
            }
        });
    </script>
</body>
</html>