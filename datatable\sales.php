<?php


use App\Models\Sale;
use App\Models\Customer;
use App\Models\Payment;

require_once __DIR__ . "/../php/init.php";
use Illuminate\Database\Capsule\Manager as DB;

// Initialize query
$query = Sale::query();

$query->where("branch_id",_branch()->id());

// Apply search filter
if (input('search') && input('search')['value']??'') {
    $search = input('search')['value'];
    $query->where(function ($q) use ($search) {
        $q->where('id', '=', $search);
        $q->where('id', 'like', "%$search%");
    });
}

if (input("from")) {
    $query->where("date", ">", input("from"));
}

if (input("to")) {
    $query->where("date", "<", input("to"));
}

// Add raw select to determine payment status using selectRaw instead of DB::raw
$query->select('*', DB::raw("
    CASE
        WHEN (SELECT SUM(amount) FROM payments WHERE reltype='sales' AND relid=sales.id) = 0 THEN 'Unpaid'
        WHEN (SELECT SUM(amount) FROM payments WHERE reltype='sales' AND relid=sales.id) < (sales.total_price) THEN 'Partially Paid'
        WHEN (SELECT SUM(amount) FROM payments WHERE reltype='sales' AND relid=sales.id) = sales.total_price THEN 'Paid'
        WHEN (SELECT SUM(amount) FROM payments WHERE reltype='sales' AND relid=sales.id) > sales.total_price THEN 'Overpaid'
    END AS payment_status
"));

// Apply ordering by payment status
if (input('order', false)) {
    $columns = [
        'id',
        'date',
        'customer_id',
        'total_price',
        'payment_status',
        'status',
    ];
    $orderColumn = $columns[input('order')[0]['column']];
    $orderDirection = input('order')[0]['dir'];
    $query->orderBy($orderColumn, $orderDirection);
} else {
    $query->orderBy('id', 'desc');
}

// Apply pagination
$start = input("start", 0);
$length = input("length", 10);
$totalData = $query->count();

$sales = $query->offset($start)->limit($length)->get();

$data = [];

foreach ($sales as $key => $sale) {

    $row = [];

    $row[] = '<a href="' . base_url("sales/view.php?i=$sale->id") . '">#' . $sale->id . '</a>';
    $row[] = _d($sale->date);
    $row[] = _d($sale->date);
    $row[] = ($sale->customer_id> 0? "<a href='".base_url("customers/update.php?i=$sale->customer_id")."'>".$sale->customer->name."</a>" : "--");
    $row[] = _format_price($sale->total_price);
    $row[] = _render_payment_status($sale->total_price, Payment::where("reltype", "sales")->where("relid", $sale->id)->sum("amount") ?? 0);
    $row[] = _render_sales_status($sale->status);

    $row[] = '
    <div class="btn-group show">
        <button type="button" class="btn btn-light dropdown-toggle" data-toggle="dropdown" aria-expanded="true"><i class="fas fa-bars"></i></button>
        <div class="dropdown-menu  " x-placement="bottom-end" style="position: absolute; will-change: transform; top: 0px; left: 0px; transform: translate3d(-75px, 36px, 0px);">
            <a href="' . base_url("sales/view.php?i=" . $sale->id) . '" class="dropdown-item"><i class="fas fa-file"></i>' . tr("View") . '</a>
            <a href="' . base_url("sales/update.php?i=" . $sale->id) . '" class="dropdown-item"><i class="fa fa-edit"></i>' . tr("Update") . '</a>
            <div class="dropdown-divider"></div>
            <a href="' . base_url("sales/prints/receipt.php?i=" . $sale->id) . '" target="_blank" class="dropdown-item"><i class="fa fa-receipt"></i>' . tr("Print Receipt") . '</a>
            <a href="' . base_url("sales/prints/invoice-a4.php?i=" . $sale->id) . '" target="_blank" class="dropdown-item"><i class="fa fa-file-invoice"></i>' . tr("Print Invoice") . '</a>
            <a href="' . base_url("sales/prints/delivery-note.php?i=" . $sale->id) . '" target="_blank" class="dropdown-item"><i class="fa fa-truck"></i>' . tr("Print Delivery Note") . '</a>
            <div class="dropdown-divider"></div>
            <a href="#" class="dropdown-item"><i class="far fa-barcode-alt"></i>' . tr("Payment") . '</a>
        </div>
    </div>
    ';

    $data[] = $row;
}

echo json_encode([
    'draw' => input('draw'),
    'recordsTotal' => $totalData,
    'recordsFiltered' => $totalData,
    'data' => $data
]);

die();
