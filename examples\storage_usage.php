<?php
/**
 * Storage Library Usage Examples
 * 
 * This file demonstrates how to use the enhanced Storage library
 * with all its new features while maintaining backward compatibility.
 */

require_once __DIR__ . '/../php/init.php';

use App\Libraries\StorageLibrary;

// Initialize storage library
$storage = new StorageLibrary();

// Example 1: Basic file upload (backward compatible)
if ($_FILES['file'] ?? false) {
    $result = $storage->save($_FILES['file']);
    
    if ($result->saved()) {
        echo "File uploaded successfully!<br>";
        echo "File ID: " . $result->id . "<br>";
        echo "File Name: " . $result->file . "<br>";
        echo "File URL: " . $storage->get($result->id) . "<br>";
    } else {
        echo "Upload failed: " . implode(', ', $result->errors()) . "<br>";
    }
}

// Example 2: Upload with thumbnail generation for images
if ($_FILES['image'] ?? false) {
    // Enable thumbnail generation
    $storage->enableThumbnails(true);
    
    $result = $storage->save($_FILES['image']);
    
    if ($result->saved()) {
        echo "Image uploaded with thumbnails!<br>";
        
        // Get different thumbnail sizes
        echo "Original: " . $storage->get($result->id) . "<br>";
        echo "Thumbnail: " . $storage->get($result->id, 'thumb') . "<br>";
        echo "Medium: " . $storage->get($result->id, 'medium') . "<br>";
        echo "Large: " . $storage->get($result->id, 'large') . "<br>";
    }
}

// Example 3: Custom thumbnail sizes
$storage->enableThumbnails(true, [
    'small' => ['width' => 100, 'height' => 100],
    'banner' => ['width' => 1200, 'height' => 400],
    'square' => ['width' => 500, 'height' => 500]
]);

// Example 4: Set custom allowed MIME types
$storage->setAllowedTypes([
    'image/jpeg' => ['jpg', 'jpeg'],
    'image/png' => ['png'],
    'application/pdf' => ['pdf']
]);

// Example 5: Multiple file upload
if ($_FILES['files'] ?? false) {
    $results = $storage->saveMultiple($_FILES['files']);
    
    foreach ($results as $result) {
        if ($result['status']) {
            echo "Uploaded: " . $result['file'] . "<br>";
        } else {
            echo "Failed: " . implode(', ', $result['errors']) . "<br>";
        }
    }
}

// Example 6: Get detailed file information
$fileId = 123; // Example file ID
$fileInfo = $storage->getFileInfo($fileId);

if ($fileInfo) {
    echo "<pre>";
    print_r($fileInfo);
    echo "</pre>";
    
    // Output:
    // [
    //     'id' => 123,
    //     'name' => 'document.pdf',
    //     'file' => '2024/01/abc123.pdf',
    //     'size' => 1024000,
    //     'mime_type' => 'application/pdf',
    //     'extension' => 'pdf',
    //     'url' => 'http://site.com/uploads/2024/01/abc123.pdf',
    //     'created_at' => '2024-01-15 10:30:00',
    //     'exists' => true,
    //     'width' => 800,  // For images only
    //     'height' => 600, // For images only
    //     'thumbnails' => [ // For images only
    //         'thumb' => 'http://site.com/uploads/2024/01/thumb/abc123.jpg',
    //         'medium' => 'http://site.com/uploads/2024/01/medium/abc123.jpg'
    //     ]
    // ]
}

// Example 7: Delete file with cleanup
$deleted = $storage->delete($fileId);
if ($deleted) {
    echo "File and all thumbnails deleted successfully<br>";
}

// Example 8: Create file input with multiple file support
echo $storage->input([
    'name' => 'documents',
    'multiple' => true,
    'class' => 'form-control',
    'accept' => '.pdf,.doc,.docx'
]);

// Example 9: Custom file size limit
$storage->setMaxSize(10 * 1024 * 1024); // 10MB

// Example 10: Using the library in a form
?>

<!DOCTYPE html>
<html>
<head>
    <title>Storage Library Examples</title>
</head>
<body>
    <h2>Single File Upload</h2>
    <form method="post" enctype="multipart/form-data">
        <?= $storage->input(['name' => 'document', 'class' => 'form-control']) ?>
        <button type="submit">Upload</button>
    </form>

    <h2>Multiple File Upload</h2>
    <form method="post" enctype="multipart/form-data">
        <?= $storage->input(['name' => 'images', 'multiple' => true, 'accept' => 'image/*']) ?>
        <button type="submit">Upload Images</button>
    </form>

    <h2>Product Image Upload with Thumbnails</h2>
    <form method="post" enctype="multipart/form-data">
        <?php
        // Configure for product images
        $productStorage = new StorageLibrary();
        $productStorage->enableThumbnails(true, [
            'catalog' => ['width' => 300, 'height' => 300],
            'detail' => ['width' => 800, 'height' => 800],
            'cart' => ['width' => 100, 'height' => 100]
        ]);
        $productStorage->setAllowedTypes([
            'image/jpeg' => ['jpg', 'jpeg'],
            'image/png' => ['png'],
            'image/webp' => ['webp']
        ]);
        
        echo $productStorage->input(['name' => 'product_image', 'class' => 'form-control']);
        ?>
        <button type="submit">Upload Product Image</button>
    </form>
</body>
</html>

<?php
// Example 11: Using in existing code (backward compatible)
// Old code still works without any changes:
function oldUploadFunction($file) {
    $storage = new StorageLibrary();
    $result = $storage->save($file);
    
    if ($result->saved()) {
        return $result->id;
    }
    
    return false;
}

// Example 12: Advanced usage with method chaining
$advancedStorage = (new StorageLibrary())
    ->setMaxSize(5 * 1024 * 1024) // 5MB
    ->setAllowedTypes([
        'image/jpeg' => ['jpg', 'jpeg'],
        'image/png' => ['png']
    ])
    ->enableThumbnails(true, [
        'preview' => ['width' => 200, 'height' => 200]
    ]);

// The library is fully backward compatible, so existing code continues to work
// while new features are available when needed.